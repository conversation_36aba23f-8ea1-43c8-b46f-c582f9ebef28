{"name": "<PERSON><PERSON><PERSON><PERSON>", "main": "papaparse.js", "homepage": "http://papaparse.com", "authors": ["<PERSON>"], "description": "Fast and powerful CSV parser for the browser. Converts CSV->JSON and JSON->CSV. Supports web workers and streaming large files.", "keywords": ["csv", "parse", "parsing", "parser", "delimited", "text", "data", "auto-detect", "comma", "tab", "pipe", "file", "filereader", "stream", "worker", "workers", "ajax", "thread", "threading", "multi-threaded"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests", "player"]}