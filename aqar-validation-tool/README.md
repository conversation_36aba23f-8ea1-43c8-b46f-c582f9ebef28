# NAAC AQAR CSV Validation Tool

A comprehensive web application for validating CSV files against NAAC AQAR (Annual Quality Assurance Report) requirements for Symbiosis International University.

## 🎯 Overview

This tool provides automated validation of CSV data submissions for all 53 NAAC AQAR metrics across 43 SIU institutions. It ensures data quality, consistency, and compliance with NAAC standards before final submission to the Quality Management & Benchmarking (QMB) department.

## ✨ Features

### Core Functionality
- **Comprehensive Validation**: 928+ validation rules covering all NAAC AQAR metrics
- **Institution Support**: All 43 SIU institutions with proper validation
- **Metric Coverage**: Complete support for all 53 NAAC metrics
- **Real-time Validation**: Instant feedback on data quality issues
- **Template Generation**: Dynamic CSV templates for each metric
- **Error Reporting**: Detailed error reports with recommendations

### Validation Types
- Year range validation (2019-2024)
- Exact match validation for choice fields
- Text content validation with word limits
- Faculty and institute code validation
- Numeric range validation
- Date format validation (DD/MM/YYYY)
- URL pattern validation
- File type validation (PDF, images)
- Currency amount validation
- Required field validation

## 🚀 Quick Start

### Prerequisites
- Node.js 18.0.0 or higher
- npm 8.0.0 or higher

### Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   # Edit .env.local with your configuration
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📋 Usage Guide

### For Institution Users

1. **Select Your Institution**
   - Choose your institution from the dropdown (43 SIU institutions available)

2. **Select Metric**
   - Choose the NAAC metric you want to validate

3. **Download Template (Optional)**
   - Click "Download Template" to get the CSV template for your metric

4. **Upload CSV File**
   - Upload your completed CSV file (max 10MB)

5. **Validate Data**
   - Click "Validate CSV" to run comprehensive validation

6. **Submit to QMB**
   - If validation passes, submit directly to QMB

## 🏗️ Technology Stack
- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **CSV Processing**: PapaParse
- **Excel Export**: SheetJS

## 📞 Support

### Contact Information
- **Primary Contact**: Dr. Dharmendra Pandey
- **Email**: <EMAIL>
- **Support Email**: <EMAIL>
- **Department**: Quality Management & Benchmarking (QMB)
- **Organization**: Symbiosis International (Deemed University)

## 🚀 Deployment

### Development
```bash
npm run dev
```

### Production
```bash
npm run build
npm start
```

---

**Version**: 1.0.0
**Maintained by**: Quality Management & Benchmarking (QMB), SIU
