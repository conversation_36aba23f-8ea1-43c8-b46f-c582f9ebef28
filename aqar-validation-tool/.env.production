# NAAC AQAR Validation Tool - Production Environment Configuration
# Author: Dr<PERSON> <PERSON><PERSON>, Symbiosis International (Deemed University)

# Application Configuration
NEXT_PUBLIC_APP_NAME="NAAC AQAR Validation Tool"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_ORGANIZATION="Symbiosis International University"
NEXT_PUBLIC_CONTACT_EMAIL="<EMAIL>"
NEXT_PUBLIC_SUPPORT_EMAIL="<EMAIL>"

# API Configuration
NEXT_PUBLIC_API_BASE_URL="https://your-domain.com/api"
API_TIMEOUT=30000

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=".csv"
UPLOAD_DIR="/var/uploads/aqar"

# Validation Configuration
VALIDATION_TIMEOUT=60000
MAX_VALIDATION_ERRORS=1000
ENABLE_DETAILED_LOGGING=false

# Database Configuration (for future use)
# DATABASE_URL="*******************************************/aqar_validation"
# DATABASE_POOL_SIZE=20
# DATABASE_SSL=true

# Email Configuration (for future use)
# SMTP_HOST="smtp.siu.edu.in"
# SMTP_PORT=587
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-secure-password"
# <AUTHOR> <EMAIL>"

# Security Configuration
SESSION_SECRET="your-super-secure-session-key-for-production"
ENCRYPTION_KEY="your-32-character-production-key-here"

# Feature Flags
ENABLE_BULK_UPLOAD=true
ENABLE_TEMPLATE_DOWNLOAD=true
ENABLE_ERROR_REPORTING=true
ENABLE_STATISTICS=true
ENABLE_ADMIN_PANEL=true

# Logging Configuration
LOG_LEVEL="warn"
LOG_FILE="/var/log/aqar-validation/app.log"
ENABLE_FILE_LOGGING=true

# Performance Configuration
ENABLE_CACHING=true
CACHE_TTL=7200
ENABLE_COMPRESSION=true

# Production Configuration
NODE_ENV="production"
NEXT_PUBLIC_DEBUG_MODE=false

# Monitoring Configuration (for future use)
# SENTRY_DSN="your-sentry-dsn"
# ENABLE_MONITORING=true
# HEALTH_CHECK_ENDPOINT="/api/health"

# Backup Configuration (for future use)
# BACKUP_ENABLED=true
# BACKUP_SCHEDULE="0 2 * * *"
# BACKUP_RETENTION_DAYS=30
