{"name": "aqar-validation-tool", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "export": "next export", "deploy:staging": "npm run build && npm run export", "deploy:production": "npm run lint && npm run type-check && npm run build", "clean": "rm -rf .next out node_modules/.cache", "postinstall": "npm run type-check"}, "dependencies": {"@types/papaparse": "^5.3.16", "lucide-react": "^0.518.0", "next": "15.3.4", "papaparse": "^5.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5", "cross-env": "^7.0.3", "jest": "^29.0.0", "@testing-library/react": "^13.0.0", "@testing-library/jest-dom": "^6.0.0", "jest-environment-jsdom": "^29.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["NAAC", "AQAR", "validation", "CSV", "education", "quality-assurance", "symbiosis", "university"], "author": "<PERSON>. <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "description": "NAAC AQAR CSV Validation Tool for Symbiosis International University", "homepage": "https://aqar-validation.siu.edu.in"}