/**
 * API Route for QMB Submission
 * Handles submission of validated files to QMB
 * Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
 */

import { NextRequest, NextResponse } from 'next/server';
import { FileManager } from '@/lib/fileManager';
import { getInstitutionByCode } from '@/data/institutions';
import { getMetricByNumber } from '@/data/metrics';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { submissionId, userInfo } = body;

    if (!submissionId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required parameter: submissionId' 
        },
        { status: 400 }
      );
    }

    // Get submission details
    const submissions = FileManager.getSubmissions();
    const submission = submissions.find(sub => sub.id === submissionId);

    if (!submission) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Submission not found: ${submissionId}` 
        },
        { status: 404 }
      );
    }

    // Check if validation passed
    if (submission.validation_status !== 'passed') {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Cannot submit file with validation errors. Please fix errors and re-validate.' 
        },
        { status: 400 }
      );
    }

    // Check if already submitted
    if (submission.submitted_to_qmb) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'This submission has already been submitted to QMB' 
        },
        { status: 400 }
      );
    }

    // Mark as submitted to QMB
    const success = FileManager.markAsSubmittedToQMB(submissionId);
    
    if (!success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to update submission status' 
        },
        { status: 500 }
      );
    }

    // In a real implementation, you would:
    // 1. Send email notification to QMB team
    // 2. Move file to QMB review folder
    // 3. Create audit log entry
    // 4. Send confirmation email to submitter

    // Simulate QMB submission process
    await simulateQMBSubmission(submission, userInfo);

    return NextResponse.json({
      success: true,
      data: {
        submissionId,
        submittedAt: new Date().toISOString(),
        status: 'submitted_to_qmb'
      },
      message: 'File successfully submitted to QMB for review'
    });

  } catch (error) {
    console.error('Submit API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error',
        message: 'Submission to QMB failed'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const instituteCode = searchParams.get('instituteCode');
    const metricNumber = searchParams.get('metricNumber');
    const status = searchParams.get('status'); // 'submitted' or 'pending'

    let submissions = FileManager.getSubmissions();

    // Filter by institute code
    if (instituteCode) {
      submissions = submissions.filter(sub => sub.institute_code === instituteCode);
    }

    // Filter by metric number
    if (metricNumber) {
      submissions = submissions.filter(sub => sub.metric_number === metricNumber);
    }

    // Filter by submission status
    if (status === 'submitted') {
      submissions = submissions.filter(sub => sub.submitted_to_qmb);
    } else if (status === 'pending') {
      submissions = submissions.filter(sub => !sub.submitted_to_qmb && sub.validation_status === 'passed');
    }

    // Sort by timestamp (newest first)
    submissions.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    return NextResponse.json({
      success: true,
      data: submissions,
      message: `Retrieved ${submissions.length} submissions`
    });

  } catch (error) {
    console.error('Get submissions API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error',
        message: 'Failed to retrieve submissions'
      },
      { status: 500 }
    );
  }
}

/**
 * Simulate QMB submission process
 * In production, this would handle actual submission workflow
 */
async function simulateQMBSubmission(submission: any, userInfo?: string): Promise<void> {
  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Get institution and metric details
  const institution = getInstitutionByCode(submission.institute_code);
  const metric = getMetricByNumber(submission.metric_number);

  // Log submission details (in production, save to database/audit log)
  console.log('QMB Submission Details:', {
    submissionId: submission.id,
    instituteCode: submission.institute_code,
    instituteName: institution?.institute_name,
    metricNumber: submission.metric_number,
    metricDescription: metric?.metric_description,
    fileName: submission.file_name,
    submittedAt: new Date().toISOString(),
    userInfo: userInfo || 'Not provided',
    validationSummary: {
      totalRows: submission.validation_result?.processedRows,
      errorCount: submission.error_count,
      warningCount: submission.warning_count
    }
  });

  // In production, implement:
  // 1. Email notification to QMB team
  // 2. File transfer to secure QMB folder
  // 3. Database record creation
  // 4. Confirmation email to submitter
  // 5. Integration with institutional workflow system
}
