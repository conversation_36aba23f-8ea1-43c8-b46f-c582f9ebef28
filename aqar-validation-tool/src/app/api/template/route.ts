/**
 * API Route for Template Generation and Download
 * Handles CSV template generation requests
 * Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
 */

import { NextRequest, NextResponse } from 'next/server';
import { TemplateGenerator } from '@/lib/templateGenerator';
import { getMetricByNumber } from '@/data/metrics';
import { CSVProcessor } from '@/utils/csvProcessor';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const metricNumber = searchParams.get('metricNumber');
    const format = searchParams.get('format') || 'csv'; // csv or json
    const includeInstructions = searchParams.get('includeInstructions') === 'true';

    if (!metricNumber) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required parameter: metricNumber' 
        },
        { status: 400 }
      );
    }

    // Validate metric
    const metric = getMetricByNumber(metricNumber);
    if (!metric) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Invalid metric number: ${metricNumber}` 
        },
        { status: 400 }
      );
    }

    // Generate template
    const template = TemplateGenerator.generateTemplate(metricNumber);

    if (format === 'json') {
      // Return template as JSON
      return NextResponse.json({
        success: true,
        data: template,
        message: `Template generated for metric ${metricNumber}`
      });
    }

    // Generate CSV content
    let csvContent: string;
    
    if (includeInstructions) {
      csvContent = TemplateGenerator.generateTemplateWithInstructions(metricNumber);
    } else {
      const headers = template.columns.map(col => col.name);
      csvContent = CSVProcessor.convertToCSV(headers, template.sampleData);
    }

    // Return CSV file
    const fileName = `AQAR_Template_${metricNumber}_${new Date().toISOString().split('T')[0]}.csv`;
    
    return new NextResponse(csvContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('Template API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error',
        message: 'Failed to generate template'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { metricNumbers, format = 'zip' } = body;

    if (!metricNumbers || !Array.isArray(metricNumbers) || metricNumbers.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing or invalid metricNumbers array' 
        },
        { status: 400 }
      );
    }

    // Validate all metrics
    const invalidMetrics = metricNumbers.filter(num => !getMetricByNumber(num));
    if (invalidMetrics.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Invalid metric numbers: ${invalidMetrics.join(', ')}` 
        },
        { status: 400 }
      );
    }

    // Generate templates for all metrics
    const templates = metricNumbers.map(metricNumber => {
      const template = TemplateGenerator.generateTemplate(metricNumber);
      const headers = template.columns.map(col => col.name);
      const csvContent = CSVProcessor.convertToCSV(headers, template.sampleData);
      
      return {
        metricNumber,
        fileName: `AQAR_Template_${metricNumber}.csv`,
        content: csvContent,
        template
      };
    });

    if (format === 'json') {
      // Return all templates as JSON
      return NextResponse.json({
        success: true,
        data: templates,
        message: `Generated ${templates.length} templates`
      });
    }

    // For now, return the first template as CSV
    // In a full implementation, you would create a ZIP file with all templates
    const firstTemplate = templates[0];
    const fileName = `AQAR_Templates_${new Date().toISOString().split('T')[0]}.csv`;
    
    return new NextResponse(firstTemplate.content, {
      status: 200,
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Cache-Control': 'no-cache'
      }
    });

  } catch (error) {
    console.error('Bulk template API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error',
        message: 'Failed to generate templates'
      },
      { status: 500 }
    );
  }
}
