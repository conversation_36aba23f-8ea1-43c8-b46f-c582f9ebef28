/**
 * API Route for Statistics and Analytics
 * Provides validation and submission statistics
 * Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
 */

import { NextRequest, NextResponse } from 'next/server';
import { FileManager } from '@/lib/fileManager';
import { INSTITUTIONS } from '@/data/institutions';
import { METRICS } from '@/data/metrics';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'overview'; // overview, institute, metric, detailed

    switch (type) {
      case 'overview':
        return getOverviewStats();
      case 'institute':
        return getInstituteStats(searchParams.get('code'));
      case 'metric':
        return getMetricStats(searchParams.get('number'));
      case 'detailed':
        return getDetailedStats();
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid stats type' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Stats API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error',
        message: 'Failed to retrieve statistics'
      },
      { status: 500 }
    );
  }
}

/**
 * Get overview statistics
 */
function getOverviewStats() {
  const stats = FileManager.getSubmissionStatistics();
  
  const overviewData = {
    summary: {
      totalInstitutions: INSTITUTIONS.length,
      totalMetrics: METRICS.length,
      totalSubmissions: stats.totalSubmissions,
      passedValidations: stats.passedValidations,
      failedValidations: stats.failedValidations,
      submittedToQMB: stats.submittedToQMB,
      successRate: stats.totalSubmissions > 0 
        ? Math.round((stats.passedValidations / stats.totalSubmissions) * 100) 
        : 0
    },
    trends: {
      recentSubmissions: stats.recentSubmissions.map(sub => ({
        id: sub.id,
        timestamp: sub.timestamp,
        instituteCode: sub.institute_code,
        metricNumber: sub.metric_number,
        status: sub.validation_status,
        submittedToQMB: sub.submitted_to_qmb
      }))
    },
    topInstitutes: Object.entries(stats.byInstitute)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([code, count]) => {
        const institute = INSTITUTIONS.find(inst => inst.institute_code === code);
        return {
          code,
          name: institute?.institute_short_name || 'Unknown',
          fullName: institute?.institute_name || 'Unknown',
          submissions: count
        };
      }),
    topMetrics: Object.entries(stats.byMetric)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([number, count]) => {
        const metric = METRICS.find(m => m.metric_number === number);
        return {
          number,
          description: metric?.metric_description || 'Unknown',
          submissions: count
        };
      })
  };

  return NextResponse.json({
    success: true,
    data: overviewData,
    message: 'Overview statistics retrieved successfully'
  });
}

/**
 * Get institute-specific statistics
 */
function getInstituteStats(instituteCode: string | null) {
  if (!instituteCode) {
    return NextResponse.json(
      { success: false, error: 'Institute code is required' },
      { status: 400 }
    );
  }

  const institution = INSTITUTIONS.find(inst => inst.institute_code === instituteCode);
  if (!institution) {
    return NextResponse.json(
      { success: false, error: 'Invalid institute code' },
      { status: 404 }
    );
  }

  const submissions = FileManager.getSubmissionsByInstitute(instituteCode);
  
  const instituteData = {
    institute: {
      code: institution.institute_code,
      shortName: institution.institute_short_name,
      fullName: institution.institute_name,
      faculty: institution.faculty,
      city: institution.city
    },
    statistics: {
      totalSubmissions: submissions.length,
      passedValidations: submissions.filter(sub => sub.validation_status === 'passed').length,
      failedValidations: submissions.filter(sub => sub.validation_status === 'failed').length,
      submittedToQMB: submissions.filter(sub => sub.submitted_to_qmb).length,
      successRate: submissions.length > 0 
        ? Math.round((submissions.filter(sub => sub.validation_status === 'passed').length / submissions.length) * 100)
        : 0
    },
    metricBreakdown: submissions.reduce((acc, sub) => {
      const metric = METRICS.find(m => m.metric_number === sub.metric_number);
      if (!acc[sub.metric_number]) {
        acc[sub.metric_number] = {
          metricNumber: sub.metric_number,
          metricDescription: metric?.metric_description || 'Unknown',
          totalSubmissions: 0,
          passed: 0,
          failed: 0,
          submitted: 0
        };
      }
      acc[sub.metric_number].totalSubmissions++;
      if (sub.validation_status === 'passed') acc[sub.metric_number].passed++;
      if (sub.validation_status === 'failed') acc[sub.metric_number].failed++;
      if (sub.submitted_to_qmb) acc[sub.metric_number].submitted++;
      return acc;
    }, {} as any),
    recentActivity: submissions
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 20)
      .map(sub => ({
        id: sub.id,
        timestamp: sub.timestamp,
        metricNumber: sub.metric_number,
        fileName: sub.original_file_name,
        status: sub.validation_status,
        errorCount: sub.error_count,
        warningCount: sub.warning_count,
        submittedToQMB: sub.submitted_to_qmb
      }))
  };

  return NextResponse.json({
    success: true,
    data: instituteData,
    message: `Statistics for ${institution.institute_short_name} retrieved successfully`
  });
}

/**
 * Get metric-specific statistics
 */
function getMetricStats(metricNumber: string | null) {
  if (!metricNumber) {
    return NextResponse.json(
      { success: false, error: 'Metric number is required' },
      { status: 400 }
    );
  }

  const metric = METRICS.find(m => m.metric_number === metricNumber);
  if (!metric) {
    return NextResponse.json(
      { success: false, error: 'Invalid metric number' },
      { status: 404 }
    );
  }

  const submissions = FileManager.getSubmissionsByMetric(metricNumber);
  
  const metricData = {
    metric: {
      number: metric.metric_number,
      description: metric.metric_description,
      criteria: metric.criteria_number
    },
    statistics: {
      totalSubmissions: submissions.length,
      passedValidations: submissions.filter(sub => sub.validation_status === 'passed').length,
      failedValidations: submissions.filter(sub => sub.validation_status === 'failed').length,
      submittedToQMB: submissions.filter(sub => sub.submitted_to_qmb).length,
      successRate: submissions.length > 0 
        ? Math.round((submissions.filter(sub => sub.validation_status === 'passed').length / submissions.length) * 100)
        : 0
    },
    instituteBreakdown: submissions.reduce((acc, sub) => {
      const institution = INSTITUTIONS.find(inst => inst.institute_code === sub.institute_code);
      if (!acc[sub.institute_code]) {
        acc[sub.institute_code] = {
          instituteCode: sub.institute_code,
          instituteName: institution?.institute_short_name || 'Unknown',
          totalSubmissions: 0,
          passed: 0,
          failed: 0,
          submitted: 0
        };
      }
      acc[sub.institute_code].totalSubmissions++;
      if (sub.validation_status === 'passed') acc[sub.institute_code].passed++;
      if (sub.validation_status === 'failed') acc[sub.institute_code].failed++;
      if (sub.submitted_to_qmb) acc[sub.institute_code].submitted++;
      return acc;
    }, {} as any),
    commonErrors: submissions
      .filter(sub => sub.validation_result?.errors)
      .flatMap(sub => sub.validation_result!.errors)
      .reduce((acc, error) => {
        const key = `${error.rule}: ${error.error}`;
        acc[key] = (acc[key] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number }),
    recentActivity: submissions
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 20)
      .map(sub => ({
        id: sub.id,
        timestamp: sub.timestamp,
        instituteCode: sub.institute_code,
        fileName: sub.original_file_name,
        status: sub.validation_status,
        errorCount: sub.error_count,
        warningCount: sub.warning_count,
        submittedToQMB: sub.submitted_to_qmb
      }))
  };

  return NextResponse.json({
    success: true,
    data: metricData,
    message: `Statistics for metric ${metricNumber} retrieved successfully`
  });
}

/**
 * Get detailed statistics for admin dashboard
 */
function getDetailedStats() {
  const stats = FileManager.getSubmissionStatistics();
  const submissions = FileManager.getSubmissions();

  // Calculate time-based statistics
  const now = new Date();
  const last24Hours = submissions.filter(sub => 
    (now.getTime() - new Date(sub.timestamp).getTime()) < 24 * 60 * 60 * 1000
  );
  const last7Days = submissions.filter(sub => 
    (now.getTime() - new Date(sub.timestamp).getTime()) < 7 * 24 * 60 * 60 * 1000
  );
  const last30Days = submissions.filter(sub => 
    (now.getTime() - new Date(sub.timestamp).getTime()) < 30 * 24 * 60 * 60 * 1000
  );

  const detailedData = {
    overview: {
      totalInstitutions: INSTITUTIONS.length,
      activeInstitutions: Object.keys(stats.byInstitute).length,
      totalMetrics: METRICS.length,
      activeMetrics: Object.keys(stats.byMetric).length,
      totalSubmissions: stats.totalSubmissions,
      successRate: stats.totalSubmissions > 0 
        ? Math.round((stats.passedValidations / stats.totalSubmissions) * 100) 
        : 0
    },
    timeBasedStats: {
      last24Hours: {
        total: last24Hours.length,
        passed: last24Hours.filter(sub => sub.validation_status === 'passed').length,
        failed: last24Hours.filter(sub => sub.validation_status === 'failed').length
      },
      last7Days: {
        total: last7Days.length,
        passed: last7Days.filter(sub => sub.validation_status === 'passed').length,
        failed: last7Days.filter(sub => sub.validation_status === 'failed').length
      },
      last30Days: {
        total: last30Days.length,
        passed: last30Days.filter(sub => sub.validation_status === 'passed').length,
        failed: last30Days.filter(sub => sub.validation_status === 'failed').length
      }
    },
    institutionStats: stats.byInstitute,
    metricStats: stats.byMetric,
    validationErrors: submissions
      .filter(sub => sub.validation_result?.errors)
      .flatMap(sub => sub.validation_result!.errors)
      .reduce((acc, error) => {
        acc[error.rule] = (acc[error.rule] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number }),
    submissionTrends: generateSubmissionTrends(submissions)
  };

  return NextResponse.json({
    success: true,
    data: detailedData,
    message: 'Detailed statistics retrieved successfully'
  });
}

/**
 * Generate submission trends data
 */
function generateSubmissionTrends(submissions: any[]) {
  const trends = [];
  const now = new Date();
  
  for (let i = 29; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];
    
    const daySubmissions = submissions.filter(sub => {
      const subDate = new Date(sub.timestamp).toISOString().split('T')[0];
      return subDate === dateStr;
    });
    
    trends.push({
      date: dateStr,
      total: daySubmissions.length,
      passed: daySubmissions.filter(sub => sub.validation_status === 'passed').length,
      failed: daySubmissions.filter(sub => sub.validation_status === 'failed').length
    });
  }
  
  return trends;
}
