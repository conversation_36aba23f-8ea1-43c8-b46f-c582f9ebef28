/**
 * API Route for CSV Validation
 * Handles CSV file validation requests
 * Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
 */

import { NextRequest, NextResponse } from 'next/server';
import { ValidationEngine } from '@/lib/validationEngine';
import { CSVProcessor } from '@/utils/csvProcessor';
import { FileManager } from '@/lib/fileManager';
import { getInstitutionByCode } from '@/data/institutions';
import { getMetricByNumber } from '@/data/metrics';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const instituteCode = formData.get('instituteCode') as string;
    const metricNumber = formData.get('metricNumber') as string;

    // Validate input parameters
    if (!file || !instituteCode || !metricNumber) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required parameters: file, instituteCode, or metricNumber' 
        },
        { status: 400 }
      );
    }

    // Validate institution and metric
    const institution = getInstitutionByCode(instituteCode);
    const metric = getMetricByNumber(metricNumber);

    if (!institution) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Invalid institute code: ${instituteCode}` 
        },
        { status: 400 }
      );
    }

    if (!metric) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Invalid metric number: ${metricNumber}` 
        },
        { status: 400 }
      );
    }

    // Validate file
    const fileValidation = CSVProcessor.validateCSVFile(file);
    if (!fileValidation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          error: fileValidation.error 
        },
        { status: 400 }
      );
    }

    // Parse CSV file
    const csvData = await CSVProcessor.parseCSVFile(file);

    // Validate CSV data
    const validationEngine = new ValidationEngine();
    const validationResult = await validationEngine.validateCSV(
      csvData,
      metricNumber,
      instituteCode
    );

    // Upload file if validation passes or has only warnings
    let uploadResult = null;
    if (validationResult.isValid || validationResult.errorCount === 0) {
      uploadResult = await FileManager.uploadFile(file, instituteCode, metricNumber);
    }

    // Save submission record
    const submission = FileManager.saveSubmission(
      instituteCode,
      institution.institute_name,
      metricNumber,
      validationResult,
      uploadResult?.filePath || '',
      file.name
    );

    return NextResponse.json({
      success: true,
      data: {
        validationResult,
        submission,
        uploadResult,
        csvData: {
          fileName: csvData.fileName,
          fileSize: csvData.fileSize,
          rowCount: csvData.rowCount,
          columnCount: csvData.headers.length
        }
      },
      message: validationResult.isValid 
        ? 'Validation completed successfully' 
        : 'Validation completed with errors'
    });

  } catch (error) {
    console.error('Validation API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error',
        message: 'Validation failed due to server error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const instituteCode = searchParams.get('instituteCode');
    const metricNumber = searchParams.get('metricNumber');

    let submissions;
    
    if (instituteCode && metricNumber) {
      // Get submissions for specific institute and metric
      submissions = FileManager.getSubmissions().filter(
        sub => sub.institute_code === instituteCode && sub.metric_number === metricNumber
      );
    } else if (instituteCode) {
      // Get submissions for specific institute
      submissions = FileManager.getSubmissionsByInstitute(instituteCode);
    } else if (metricNumber) {
      // Get submissions for specific metric
      submissions = FileManager.getSubmissionsByMetric(metricNumber);
    } else {
      // Get all submissions
      submissions = FileManager.getSubmissions();
    }

    return NextResponse.json({
      success: true,
      data: submissions,
      message: `Retrieved ${submissions.length} submissions`
    });

  } catch (error) {
    console.error('Get submissions API error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error',
        message: 'Failed to retrieve submissions'
      },
      { status: 500 }
    );
  }
}
