/**
 * CSV Processing Utilities
 * Handles CSV parsing, validation, and processing
 * Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
 */

import <PERSON> from 'papaparse';
import { CSVData } from '@/types';

export class CSVProcessor {
  /**
   * Parse CSV file and return structured data
   */
  public static async parseCSVFile(file: File): Promise<CSVData> {
    return new Promise((resolve, reject) => {
      Papa.parse(file, {
        header: false,
        skipEmptyLines: true,
        complete: (results) => {
          try {
            if (results.errors.length > 0) {
              reject(new Error(`CSV parsing error: ${results.errors[0].message}`));
              return;
            }

            const data = results.data as string[][];
            if (data.length === 0) {
              reject(new Error('CSV file is empty'));
              return;
            }

            const headers = data[0];
            const rows = data.slice(1);

            const csvData: CSVData = {
              headers,
              rows,
              fileName: file.name,
              fileSize: file.size,
              rowCount: rows.length
            };

            resolve(csvData);
          } catch (error) {
            reject(error);
          }
        },
        error: (error) => {
          reject(new Error(`Failed to parse CSV: ${error.message}`));
        }
      });
    });
  }

  /**
   * Validate CSV file before processing
   */
  public static validateCSVFile(file: File): { isValid: boolean; error?: string } {
    // Check file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      return { isValid: false, error: 'File must be a CSV file' };
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return { isValid: false, error: 'File size must not exceed 10MB' };
    }

    // Check if file is empty
    if (file.size === 0) {
      return { isValid: false, error: 'File cannot be empty' };
    }

    return { isValid: true };
  }

  /**
   * Convert CSV data back to CSV string
   */
  public static convertToCSV(headers: string[], rows: string[][]): string {
    const allRows = [headers, ...rows];
    return Papa.unparse(allRows);
  }

  /**
   * Download CSV data as file
   */
  public static downloadCSV(data: string, filename: string): void {
    const blob = new Blob([data], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  /**
   * Clean and normalize CSV data
   */
  public static cleanCSVData(csvData: CSVData): CSVData {
    // Clean headers - remove extra spaces, normalize case
    const cleanHeaders = csvData.headers.map(header => 
      header.trim().replace(/\s+/g, '_')
    );

    // Clean rows - trim whitespace from all cells
    const cleanRows = csvData.rows.map(row => 
      row.map(cell => cell ? cell.toString().trim() : '')
    );

    return {
      ...csvData,
      headers: cleanHeaders,
      rows: cleanRows
    };
  }

  /**
   * Get CSV statistics
   */
  public static getCSVStatistics(csvData: CSVData): {
    totalRows: number;
    totalColumns: number;
    emptyRows: number;
    emptyColumns: number;
    columnStats: { [key: string]: { filled: number; empty: number; unique: number } };
  } {
    const stats = {
      totalRows: csvData.rows.length,
      totalColumns: csvData.headers.length,
      emptyRows: 0,
      emptyColumns: 0,
      columnStats: {} as { [key: string]: { filled: number; empty: number; unique: number } }
    };

    // Count empty rows
    stats.emptyRows = csvData.rows.filter(row => 
      row.every(cell => !cell || !cell.trim())
    ).length;

    // Analyze each column
    csvData.headers.forEach((header, colIndex) => {
      const columnValues = csvData.rows.map(row => row[colIndex] || '');
      const filledValues = columnValues.filter(value => value.trim() !== '');
      const uniqueValues = new Set(filledValues);

      stats.columnStats[header] = {
        filled: filledValues.length,
        empty: columnValues.length - filledValues.length,
        unique: uniqueValues.size
      };
    });

    return stats;
  }

  /**
   * Validate CSV structure against expected format
   */
  public static validateCSVStructure(
    csvData: CSVData, 
    expectedHeaders: string[]
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check if all expected headers are present
    const missingHeaders = expectedHeaders.filter(header => 
      !csvData.headers.includes(header)
    );

    if (missingHeaders.length > 0) {
      errors.push(`Missing required columns: ${missingHeaders.join(', ')}`);
    }

    // Check for extra headers (optional warning)
    const extraHeaders = csvData.headers.filter(header => 
      !expectedHeaders.includes(header)
    );

    if (extraHeaders.length > 0) {
      errors.push(`Unexpected columns found: ${extraHeaders.join(', ')}`);
    }

    // Check if all rows have the same number of columns as headers
    const inconsistentRows = csvData.rows.filter(row => 
      row.length !== csvData.headers.length
    );

    if (inconsistentRows.length > 0) {
      errors.push(`${inconsistentRows.length} rows have inconsistent column count`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Sample CSV data for preview
   */
  public static sampleCSVData(csvData: CSVData, maxRows: number = 5): CSVData {
    return {
      ...csvData,
      rows: csvData.rows.slice(0, maxRows),
      rowCount: Math.min(csvData.rowCount, maxRows)
    };
  }

  /**
   * Convert CSV data to table format for display
   */
  public static csvToTableData(csvData: CSVData): {
    headers: string[];
    rows: { [key: string]: string }[];
  } {
    const tableRows = csvData.rows.map(row => {
      const rowObject: { [key: string]: string } = {};
      csvData.headers.forEach((header, index) => {
        rowObject[header] = row[index] || '';
      });
      return rowObject;
    });

    return {
      headers: csvData.headers,
      rows: tableRows
    };
  }

  /**
   * Filter CSV data based on criteria
   */
  public static filterCSVData(
    csvData: CSVData, 
    filters: { [columnName: string]: string }
  ): CSVData {
    const filteredRows = csvData.rows.filter(row => {
      return Object.entries(filters).every(([columnName, filterValue]) => {
        const columnIndex = csvData.headers.indexOf(columnName);
        if (columnIndex === -1) return true; // Column not found, skip filter
        
        const cellValue = row[columnIndex] || '';
        return cellValue.toLowerCase().includes(filterValue.toLowerCase());
      });
    });

    return {
      ...csvData,
      rows: filteredRows,
      rowCount: filteredRows.length
    };
  }

  /**
   * Sort CSV data by column
   */
  public static sortCSVData(
    csvData: CSVData, 
    columnName: string, 
    direction: 'asc' | 'desc' = 'asc'
  ): CSVData {
    const columnIndex = csvData.headers.indexOf(columnName);
    if (columnIndex === -1) return csvData; // Column not found

    const sortedRows = [...csvData.rows].sort((a, b) => {
      const aValue = a[columnIndex] || '';
      const bValue = b[columnIndex] || '';
      
      // Try to parse as numbers first
      const aNum = parseFloat(aValue);
      const bNum = parseFloat(bValue);
      
      if (!isNaN(aNum) && !isNaN(bNum)) {
        return direction === 'asc' ? aNum - bNum : bNum - aNum;
      }
      
      // Fall back to string comparison
      return direction === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    });

    return {
      ...csvData,
      rows: sortedRows
    };
  }

  /**
   * Get unique values for a column
   */
  public static getUniqueColumnValues(csvData: CSVData, columnName: string): string[] {
    const columnIndex = csvData.headers.indexOf(columnName);
    if (columnIndex === -1) return [];

    const values = csvData.rows.map(row => row[columnIndex] || '');
    return [...new Set(values)].filter(value => value.trim() !== '').sort();
  }

  /**
   * Detect column data types
   */
  public static detectColumnTypes(csvData: CSVData): { [columnName: string]: string } {
    const types: { [columnName: string]: string } = {};

    csvData.headers.forEach((header, colIndex) => {
      const values = csvData.rows
        .map(row => row[colIndex] || '')
        .filter(value => value.trim() !== '');

      if (values.length === 0) {
        types[header] = 'empty';
        return;
      }

      // Check if all values are numbers
      const allNumbers = values.every(value => !isNaN(parseFloat(value)));
      if (allNumbers) {
        types[header] = 'numeric';
        return;
      }

      // Check if all values are dates
      const allDates = values.every(value => {
        const date = new Date(value);
        return !isNaN(date.getTime());
      });
      if (allDates) {
        types[header] = 'date';
        return;
      }

      // Check if all values are URLs
      const allUrls = values.every(value => {
        try {
          new URL(value);
          return true;
        } catch {
          return false;
        }
      });
      if (allUrls) {
        types[header] = 'url';
        return;
      }

      // Default to text
      types[header] = 'text';
    });

    return types;
  }
}
