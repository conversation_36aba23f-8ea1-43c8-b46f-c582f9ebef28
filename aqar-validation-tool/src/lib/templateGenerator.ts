/**
 * CSV Template Generator
 * Generates CSV templates based on metric requirements
 * Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
 */

import { Template, TemplateColumn, ValidationRule } from '@/types';
import { getValidationRulesByMetric } from '@/data/validationRules';
import { getMetricByNumber } from '@/data/metrics';
import { CSVProcessor } from '@/utils/csvProcessor';

export class TemplateGenerator {
  /**
   * Generate template for a specific metric
   */
  public static generateTemplate(metricNumber: string): Template {
    const metric = getMetricByNumber(metricNumber);
    const validationRules = getValidationRulesByMetric(metricNumber);
    
    if (!metric) {
      throw new Error(`Metric ${metricNumber} not found`);
    }

    const columns = this.generateTemplateColumns(validationRules);
    const sampleData = this.generateSampleData(columns);
    const instructions = this.generateInstructions(metricNumber, validationRules);

    return {
      metricNumber,
      metricDescription: metric.metric_description,
      columns,
      instructions,
      sampleData
    };
  }

  /**
   * Generate template columns from validation rules
   */
  private static generateTemplateColumns(rules: ValidationRule[]): TemplateColumn[] {
    const columnMap = new Map<string, TemplateColumn>();

    rules.forEach(rule => {
      if (!columnMap.has(rule.column_name)) {
        columnMap.set(rule.column_name, {
          name: rule.column_name,
          description: this.getColumnDescription(rule),
          dataType: rule.data_type,
          required: rule.is_required,
          validationHint: this.getValidationHint(rule),
          sampleValue: this.generateSampleValue(rule)
        });
      }
    });

    return Array.from(columnMap.values()).sort((a, b) => {
      // Sort required columns first
      if (a.required && !b.required) return -1;
      if (!a.required && b.required) return 1;
      return a.name.localeCompare(b.name);
    });
  }

  /**
   * Get column description based on validation rule
   */
  private static getColumnDescription(rule: ValidationRule): string {
    const descriptions: { [key: string]: string } = {
      'Programme_Name': 'Name of the academic programme',
      'Academic_Year': 'Academic year in YYYY-YYYY format',
      'Institute_Code': 'Official institute code from SIU',
      'Institute_Name': 'Full name of the institute',
      'Faculty': 'Faculty/Department name',
      'Total_Students': 'Total number of students',
      'Students_With_Projects': 'Number of students with projects/internships',
      'Global_Need_Description': 'Description of global developmental needs addressed',
      'National_Need_Description': 'Description of national developmental needs addressed',
      'Evidence_Document': 'Supporting document file path',
      'Professional_Ethics': 'Whether professional ethics is integrated (Yes/No)',
      'Gender_Issues': 'Whether gender issues are addressed (Yes/No)',
      'Workshop_Title': 'Title of the workshop/seminar',
      'Date_Conducted': 'Date when workshop was conducted',
      'Bandwidth_Mbps': 'Internet bandwidth in Mbps',
      'Service_Provider': 'Internet service provider name',
      'Students_Eligible_For_Placement': 'Number of students eligible for placement',
      'Students_Placed': 'Number of students successfully placed',
      'FDP_Type': 'Type of Faculty Development Programme',
      'Duration_Hours': 'Duration of programme in hours',
      'Infrastructure_Expenditure': 'Amount spent on infrastructure',
      'Total_Expenditure': 'Total expenditure amount',
      'Remarks': 'Additional remarks or comments',
      'Supporting_Document_URL': 'URL to supporting documents'
    };

    return descriptions[rule.column_name] || `Data for ${rule.column_name.replace(/_/g, ' ')}`;
  }

  /**
   * Generate validation hint for column
   */
  private static getValidationHint(rule: ValidationRule): string {
    switch (rule.validation_type) {
      case 'year_range':
        return `Year between ${rule.validation_params}`;
      case 'exact_match':
        return `Must be one of: ${rule.validation_params}`;
      case 'numeric':
        return `Numeric value ${rule.validation_params ? `(${rule.validation_params})` : ''}`;
      case 'word_limit':
        return `Maximum ${rule.validation_params} words`;
      case 'text_length':
        return `Text length ${rule.validation_params}`;
      case 'yes_no_choice':
        return 'Enter Yes or No';
      case 'date':
        return 'Date in DD/MM/YYYY format';
      case 'currency':
        return 'Currency amount (e.g., 100000)';
      case 'url_pattern':
        return 'Valid URL starting with http:// or https://';
      case 'pdf_file':
        return 'PDF file path or name';
      case 'image_file':
        return 'Image file path (jpg, png, etc.)';
      case 'required_field':
        return 'This field is mandatory';
      case 'institute_code_choice':
        return 'Valid SIU institute code';
      case 'institute_name_choice':
        return 'Valid SIU institute name';
      case 'faculty_choice':
        return 'Valid faculty name';
      default:
        return 'Follow the specified format';
    }
  }

  /**
   * Generate sample value for column
   */
  private static generateSampleValue(rule: ValidationRule): string {
    switch (rule.validation_type) {
      case 'year_range':
        const [start] = rule.validation_params.split('-');
        return start || '2023';
      case 'exact_match':
        const options = rule.validation_params.split(',');
        return options[0]?.trim() || 'Option1';
      case 'numeric':
        return '100';
      case 'word_limit':
        return 'Sample text content';
      case 'yes_no_choice':
        return 'Yes';
      case 'date':
        return '01/01/2023';
      case 'currency':
        return '100000';
      case 'url_pattern':
        return 'https://example.com/document.pdf';
      case 'pdf_file':
        return 'document.pdf';
      case 'image_file':
        return 'image.jpg';
      case 'institute_code_choice':
        return '101';
      case 'institute_name_choice':
        return 'Symbiosis Law School, Pune';
      case 'faculty_choice':
        return 'LAW';
      default:
        return this.getDefaultSampleValue(rule.column_name);
    }
  }

  /**
   * Get default sample value based on column name
   */
  private static getDefaultSampleValue(columnName: string): string {
    const sampleValues: { [key: string]: string } = {
      'Programme_Name': 'Bachelor of Laws (LL.B.)',
      'Academic_Year': '2023-2024',
      'Institute_Code': '101',
      'Institute_Name': 'Symbiosis Law School, Pune',
      'Faculty': 'LAW',
      'Total_Students': '150',
      'Students_With_Projects': '120',
      'Workshop_Title': 'Research Methodology Workshop',
      'Service_Provider': 'Airtel Business',
      'Bandwidth_Mbps': '100',
      'Remarks': 'Additional information if any'
    };

    return sampleValues[columnName] || 'Sample Value';
  }

  /**
   * Generate sample data rows
   */
  private static generateSampleData(columns: TemplateColumn[]): string[][] {
    const sampleRows: string[][] = [];
    
    // Generate 3 sample rows
    for (let i = 0; i < 3; i++) {
      const row = columns.map(column => {
        if (column.name === 'Academic_Year') {
          return `${2021 + i}-${2022 + i}`;
        }
        if (column.name.includes('Total') || column.name.includes('Students')) {
          return (100 + i * 10).toString();
        }
        return column.sampleValue;
      });
      sampleRows.push(row);
    }

    return sampleRows;
  }

  /**
   * Generate instructions for the template
   */
  private static generateInstructions(metricNumber: string, rules: ValidationRule[]): string[] {
    const instructions = [
      `Template for Metric ${metricNumber}`,
      'Please follow these guidelines when filling the template:',
      '',
      'General Instructions:',
      '• Fill all required fields (marked with *)',
      '• Use the exact format specified for each field',
      '• Do not modify column headers',
      '• Ensure data consistency across all rows',
      '• Save the file in CSV format before uploading',
      '',
      'Field-specific Instructions:'
    ];

    // Add specific instructions for each validation type
    const validationTypes = [...new Set(rules.map(rule => rule.validation_type))];
    
    validationTypes.forEach(type => {
      switch (type) {
        case 'year_range':
          instructions.push('• Academic years should be in YYYY-YYYY format (e.g., 2023-2024)');
          break;
        case 'institute_code_choice':
          instructions.push('• Use only valid SIU institute codes (101, 102, 201, etc.)');
          break;
        case 'date':
          instructions.push('• Dates should be in DD/MM/YYYY format (e.g., 15/03/2023)');
          break;
        case 'numeric':
          instructions.push('• Numeric fields should contain only numbers');
          break;
        case 'currency':
          instructions.push('• Currency amounts should be in numbers only (without symbols)');
          break;
        case 'yes_no_choice':
          instructions.push('• Yes/No fields should contain exactly "Yes" or "No"');
          break;
        case 'word_limit':
          instructions.push('• Respect word limits for text fields');
          break;
        case 'pdf_file':
          instructions.push('• File references should include .pdf extension');
          break;
      }
    });

    instructions.push('');
    instructions.push('For support, contact: <EMAIL>');

    return instructions;
  }

  /**
   * Generate and download CSV template
   */
  public static downloadTemplate(metricNumber: string): void {
    const template = this.generateTemplate(metricNumber);
    const headers = template.columns.map(col => col.name);
    const csvContent = CSVProcessor.convertToCSV(headers, template.sampleData);
    
    const filename = `AQAR_Template_${metricNumber}_${new Date().toISOString().split('T')[0]}.csv`;
    CSVProcessor.downloadCSV(csvContent, filename);
  }

  /**
   * Generate template with instructions as comments
   */
  public static generateTemplateWithInstructions(metricNumber: string): string {
    const template = this.generateTemplate(metricNumber);
    
    let content = '';
    
    // Add instructions as comments
    template.instructions.forEach(instruction => {
      content += `# ${instruction}\n`;
    });
    
    content += '\n';
    
    // Add column descriptions as comments
    content += '# Column Descriptions:\n';
    template.columns.forEach(column => {
      const required = column.required ? ' (Required)' : ' (Optional)';
      content += `# ${column.name}${required}: ${column.description}\n`;
      content += `#   Format: ${column.validationHint}\n`;
      content += `#   Example: ${column.sampleValue}\n`;
    });
    
    content += '\n';
    
    // Add CSV data
    const headers = template.columns.map(col => col.name);
    const csvData = CSVProcessor.convertToCSV(headers, template.sampleData);
    content += csvData;
    
    return content;
  }

  /**
   * Get template summary
   */
  public static getTemplateSummary(metricNumber: string): {
    metricNumber: string;
    metricDescription: string;
    totalColumns: number;
    requiredColumns: number;
    optionalColumns: number;
    validationTypes: string[];
  } {
    const template = this.generateTemplate(metricNumber);
    const requiredColumns = template.columns.filter(col => col.required).length;
    const validationTypes = [...new Set(template.columns.map(col => {
      // Extract validation type from hint
      return col.validationHint.split(' ')[0];
    }))];

    return {
      metricNumber: template.metricNumber,
      metricDescription: template.metricDescription,
      totalColumns: template.columns.length,
      requiredColumns,
      optionalColumns: template.columns.length - requiredColumns,
      validationTypes
    };
  }
}
