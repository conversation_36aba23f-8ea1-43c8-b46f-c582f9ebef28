/**
 * NAAC AQAR CSV Validation Engine
 * Comprehensive validation system for all validation types
 * Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
 */

import { ValidationRule, ValidationError, ValidationResult, ValidationSummary, ValidatorMap, CSVData } from '@/types';
import { getValidationRulesByMetric } from '@/data/validationRules';
import { INSTITUTIONS } from '@/data/institutions';

export class ValidationEngine {
  private validators: ValidatorMap;

  constructor() {
    this.validators = {
      year_range: this.validateYearRange.bind(this),
      exact_match: this.validateExactMatch.bind(this),
      contains_text: this.validateContainsText.bind(this),
      faculty_choice: this.validateFacultyChoice.bind(this),
      institute_code_choice: this.validateInstituteCodeChoice.bind(this),
      institute_shortname_choice: this.validateInstituteShortNameChoice.bind(this),
      institute_name_choice: this.validateInstituteNameChoice.bind(this),
      fixed_digits: this.validateFixedDigits.bind(this),
      required_field: this.validateRequiredField.bind(this),
      word_limit: this.validateWordLimit.bind(this),
      pdf_file: this.validatePdfFile.bind(this),
      yes_no_choice: this.validateYesNoChoice.bind(this),
      binary_choice: this.validateBinaryChoice.bind(this),
      text_length: this.validateTextLength.bind(this),
      year: this.validateYear.bind(this),
      numeric: this.validateNumeric.bind(this),
      currency: this.validateCurrency.bind(this),
      url_pattern: this.validateUrlPattern.bind(this),
      image_file: this.validateImageFile.bind(this),
      date: this.validateDate.bind(this),
      reference_lookup: this.validateReferenceLookup.bind(this)
    };
  }

  /**
   * Main validation function
   */
  public async validateCSV(csvData: CSVData, metricNumber: string, instituteCode: string): Promise<ValidationResult> {
    const startTime = new Date();
    const validationRules = getValidationRulesByMetric(metricNumber);
    const errors: ValidationError[] = [];
    
    // Validate headers first
    const headerErrors = this.validateHeaders(csvData.headers, validationRules);
    errors.push(...headerErrors);

    // Validate each row
    for (let rowIndex = 0; rowIndex < csvData.rows.length; rowIndex++) {
      const row = csvData.rows[rowIndex];
      const rowErrors = this.validateRow(row, csvData.headers, validationRules, rowIndex + 2); // +2 for header and 1-based indexing
      errors.push(...rowErrors);
    }

    const summary = this.generateValidationSummary(errors, csvData.rows.length);
    
    return {
      isValid: errors.filter(e => e.severity === 'error').length === 0,
      errorCount: errors.filter(e => e.severity === 'error').length,
      warningCount: errors.filter(e => e.severity === 'warning').length,
      errors,
      summary,
      processedRows: csvData.rows.length,
      timestamp: startTime
    };
  }

  /**
   * Validate CSV headers against required columns
   */
  private validateHeaders(headers: string[], rules: ValidationRule[]): ValidationError[] {
    const errors: ValidationError[] = [];
    const requiredColumns = rules.filter(rule => rule.is_required).map(rule => rule.column_name);
    
    for (const requiredColumn of requiredColumns) {
      if (!headers.includes(requiredColumn)) {
        errors.push({
          row: 1,
          column: requiredColumn,
          value: '',
          error: `Required column '${requiredColumn}' is missing from CSV headers`,
          rule: 'required_field',
          severity: 'error'
        });
      }
    }
    
    return errors;
  }

  /**
   * Validate a single row against all applicable rules
   */
  private validateRow(row: string[], headers: string[], rules: ValidationRule[], rowNumber: number): ValidationError[] {
    const errors: ValidationError[] = [];
    
    for (const rule of rules) {
      const columnIndex = headers.indexOf(rule.column_name);
      if (columnIndex === -1) continue; // Column not found, already handled in header validation
      
      const value = row[columnIndex] || '';
      const validator = this.validators[rule.validation_type];
      
      if (validator) {
        const result = validator(value, rule.validation_params, rowNumber, [row]);
        if (!result.isValid) {
          errors.push({
            row: rowNumber,
            column: rule.column_name,
            value,
            error: result.error || rule.error_message,
            rule: rule.validation_type,
            severity: rule.is_required ? 'error' : 'warning'
          });
        }
      }
    }
    
    return errors;
  }

  /**
   * Generate validation summary
   */
  private generateValidationSummary(errors: ValidationError[], totalRows: number): ValidationSummary {
    const errorRows = new Set(errors.filter(e => e.severity === 'error').map(e => e.row)).size;
    const warningRows = new Set(errors.filter(e => e.severity === 'warning').map(e => e.row)).size;
    
    const commonErrors: { [key: string]: number } = {};
    errors.forEach(error => {
      const key = `${error.rule}: ${error.error}`;
      commonErrors[key] = (commonErrors[key] || 0) + 1;
    });
    
    const criticalErrors = errors
      .filter(e => e.severity === 'error')
      .slice(0, 10); // Top 10 critical errors
    
    return {
      totalRows,
      validRows: totalRows - errorRows,
      errorRows,
      warningRows,
      criticalErrors,
      commonErrors
    };
  }

  // Validation Functions
  private validateYearRange(value: string, params: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true }; // Allow empty for non-required fields
    
    const [startYear, endYear] = params.split('-').map(y => parseInt(y));
    const year = parseInt(value);
    
    if (isNaN(year)) {
      return { isValid: false, error: 'Invalid year format' };
    }
    
    if (year < startYear || year > endYear) {
      return { isValid: false, error: `Year must be between ${startYear} and ${endYear}` };
    }
    
    return { isValid: true };
  }

  private validateExactMatch(value: string, params: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const allowedValues = params.split(',').map(v => v.trim());
    if (!allowedValues.includes(value.trim())) {
      return { isValid: false, error: `Value must be one of: ${allowedValues.join(', ')}` };
    }
    
    return { isValid: true };
  }

  private validateContainsText(value: string, params: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const requiredTexts = params.split(',').map(t => t.trim().toLowerCase());
    const valueLower = value.toLowerCase();
    
    const hasRequiredText = requiredTexts.some(text => valueLower.includes(text));
    if (!hasRequiredText) {
      return { isValid: false, error: `Text must contain one of: ${requiredTexts.join(', ')}` };
    }
    
    return { isValid: true };
  }

  private validateFacultyChoice(value: string, params: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const validFaculties = params.split(',').map(f => f.trim());
    if (!validFaculties.includes(value.trim())) {
      return { isValid: false, error: `Invalid faculty. Must be one of: ${validFaculties.join(', ')}` };
    }
    
    return { isValid: true };
  }

  private validateInstituteCodeChoice(value: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const validCodes = INSTITUTIONS.map(inst => inst.institute_code);
    if (!validCodes.includes(value.trim())) {
      return { isValid: false, error: 'Invalid institute code' };
    }
    
    return { isValid: true };
  }

  private validateInstituteShortNameChoice(value: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const validNames = INSTITUTIONS.map(inst => inst.institute_short_name);
    if (!validNames.includes(value.trim())) {
      return { isValid: false, error: 'Invalid institute short name' };
    }
    
    return { isValid: true };
  }

  private validateInstituteNameChoice(value: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const validNames = INSTITUTIONS.map(inst => inst.institute_name);
    if (!validNames.includes(value.trim())) {
      return { isValid: false, error: 'Invalid institute name' };
    }
    
    return { isValid: true };
  }

  private validateFixedDigits(value: string, params: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const expectedLength = parseInt(params);
    if (!/^\d+$/.test(value) || value.length !== expectedLength) {
      return { isValid: false, error: `Must be exactly ${expectedLength} digits` };
    }
    
    return { isValid: true };
  }

  private validateRequiredField(value: string): { isValid: boolean; error?: string } {
    if (!value || !value.trim()) {
      return { isValid: false, error: 'This field is required' };
    }
    
    return { isValid: true };
  }

  private validateWordLimit(value: string, params: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const maxWords = parseInt(params);
    const wordCount = value.trim().split(/\s+/).length;
    
    if (wordCount > maxWords) {
      return { isValid: false, error: `Exceeds word limit of ${maxWords} words (current: ${wordCount})` };
    }
    
    return { isValid: true };
  }

  private validatePdfFile(value: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    if (!value.toLowerCase().endsWith('.pdf')) {
      return { isValid: false, error: 'File must be a PDF' };
    }
    
    return { isValid: true };
  }

  private validateYesNoChoice(value: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const validValues = ['Yes', 'No', 'YES', 'NO', 'yes', 'no'];
    if (!validValues.includes(value.trim())) {
      return { isValid: false, error: 'Value must be Yes or No' };
    }
    
    return { isValid: true };
  }

  private validateBinaryChoice(value: string, params: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const [option1, option2] = params.split(',').map(o => o.trim());
    if (![option1, option2].includes(value.trim())) {
      return { isValid: false, error: `Value must be ${option1} or ${option2}` };
    }
    
    return { isValid: true };
  }

  private validateTextLength(value: string, params: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const [minStr, maxStr] = params.split(',');
    const min = parseInt(minStr.split(':')[1]);
    const max = parseInt(maxStr.split(':')[1]);
    
    if (value.length < min || value.length > max) {
      return { isValid: false, error: `Text length must be between ${min} and ${max} characters` };
    }
    
    return { isValid: true };
  }

  private validateYear(value: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const year = parseInt(value);
    const currentYear = new Date().getFullYear();
    
    if (isNaN(year) || year < 1900 || year > currentYear + 10) {
      return { isValid: false, error: `Invalid year. Must be between 1900 and ${currentYear + 10}` };
    }
    
    return { isValid: true };
  }

  private validateNumeric(value: string, params: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const num = parseFloat(value);
    if (isNaN(num)) {
      return { isValid: false, error: 'Value must be a number' };
    }
    
    if (params) {
      const constraints = params.split(',');
      for (const constraint of constraints) {
        const [type, val] = constraint.split(':');
        const constraintValue = parseFloat(val);
        
        if (type === 'min' && num < constraintValue) {
          return { isValid: false, error: `Value must be at least ${constraintValue}` };
        }
        if (type === 'max' && num > constraintValue) {
          return { isValid: false, error: `Value must not exceed ${constraintValue}` };
        }
      }
    }
    
    return { isValid: true };
  }

  private validateCurrency(value: string, params: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    // Remove currency symbols and commas
    const cleanValue = value.replace(/[₹$,\s]/g, '');
    const num = parseFloat(cleanValue);
    
    if (isNaN(num)) {
      return { isValid: false, error: 'Invalid currency format' };
    }
    
    return this.validateNumeric(cleanValue, params);
  }

  private validateUrlPattern(value: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    try {
      new URL(value);
      return { isValid: true };
    } catch {
      return { isValid: false, error: 'Invalid URL format' };
    }
  }

  private validateImageFile(value: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    const hasValidExtension = validExtensions.some(ext => 
      value.toLowerCase().endsWith(ext)
    );
    
    if (!hasValidExtension) {
      return { isValid: false, error: 'File must be an image (jpg, jpeg, png, gif, bmp, webp)' };
    }
    
    return { isValid: true };
  }

  private validateDate(value: string, params: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    // Parse parameters
    const paramPairs = params.split(',');
    let format = 'DD/MM/YYYY';
    let dateRange = '';
    
    for (const param of paramPairs) {
      const [key, val] = param.split(':');
      if (key === 'format') format = val;
      if (key === 'range') dateRange = val;
    }
    
    // Validate date format
    const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
    const match = value.match(dateRegex);
    
    if (!match) {
      return { isValid: false, error: `Date must be in ${format} format` };
    }
    
    const [, day, month, year] = match;
    const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    
    if (date.getDate() !== parseInt(day) || 
        date.getMonth() !== parseInt(month) - 1 || 
        date.getFullYear() !== parseInt(year)) {
      return { isValid: false, error: 'Invalid date' };
    }
    
    // Validate date range if specified
    if (dateRange) {
      const [startDate, endDate] = dateRange.split('-');
      const [startDay, startMonth, startYear] = startDate.split('/');
      const [endDay, endMonth, endYear] = endDate.split('/');
      
      const start = new Date(parseInt(startYear), parseInt(startMonth) - 1, parseInt(startDay));
      const end = new Date(parseInt(endYear), parseInt(endMonth) - 1, parseInt(endDay));
      
      if (date < start || date > end) {
        return { isValid: false, error: `Date must be between ${startDate} and ${endDate}` };
      }
    }
    
    return { isValid: true };
  }

  private validateReferenceLookup(value: string, params: string): { isValid: boolean; error?: string } {
    if (!value.trim()) return { isValid: true };
    
    // This would typically lookup against a reference table
    // For now, we'll implement basic validation
    return { isValid: true };
  }
}
