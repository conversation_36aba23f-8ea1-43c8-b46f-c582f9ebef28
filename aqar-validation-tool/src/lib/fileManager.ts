/**
 * File Management and Submission System
 * Handles file uploads, storage, and submission tracking
 * Author: <PERSON><PERSON>, Symbiosis International (Deemed University)
 */

import { Submission, ValidationResult, FileUploadResult, ErrorReport } from '@/types';
import * as XLSX from 'xlsx';

export class FileManager {
  private static readonly STORAGE_KEY = 'aqar_submissions';
  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  private static readonly ALLOWED_TYPES = ['.csv'];

  /**
   * Upload and validate file
   */
  public static async uploadFile(file: File, instituteCode: string, metricNumber: string): Promise<FileUploadResult> {
    try {
      // Validate file
      const validation = this.validateFile(file);
      if (!validation.isValid) {
        return {
          success: false,
          fileName: file.name,
          filePath: '',
          fileSize: file.size,
          error: validation.error
        };
      }

      // Generate unique file path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${instituteCode}_${metricNumber}_${timestamp}_${file.name}`;
      const filePath = `uploads/${fileName}`;

      // In a real application, you would upload to a server or cloud storage
      // For now, we'll simulate the upload
      await this.simulateFileUpload(file, filePath);

      return {
        success: true,
        fileName: file.name,
        filePath,
        fileSize: file.size
      };
    } catch (error) {
      return {
        success: false,
        fileName: file.name,
        filePath: '',
        fileSize: file.size,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Validate uploaded file
   */
  private static validateFile(file: File): { isValid: boolean; error?: string } {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      return { isValid: false, error: `File size exceeds ${this.MAX_FILE_SIZE / (1024 * 1024)}MB limit` };
    }

    // Check file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!this.ALLOWED_TYPES.includes(fileExtension)) {
      return { isValid: false, error: `File type not allowed. Allowed types: ${this.ALLOWED_TYPES.join(', ')}` };
    }

    // Check if file is empty
    if (file.size === 0) {
      return { isValid: false, error: 'File cannot be empty' };
    }

    return { isValid: true };
  }

  /**
   * Simulate file upload (in production, this would upload to server/cloud)
   */
  private static async simulateFileUpload(file: File, filePath: string): Promise<void> {
    return new Promise((resolve) => {
      // Simulate upload delay
      setTimeout(() => {
        // In production, implement actual file upload logic here
        resolve();
      }, 1000);
    });
  }

  /**
   * Save submission record
   */
  public static saveSubmission(
    instituteCode: string,
    instituteName: string,
    metricNumber: string,
    validationResult: ValidationResult,
    filePath: string,
    originalFileName: string
  ): Submission {
    const submission: Submission = {
      id: this.generateSubmissionId(),
      timestamp: new Date(),
      institute_code: instituteCode,
      institute_name: instituteName,
      metric_number: metricNumber,
      validation_status: validationResult.isValid ? 'passed' : 'failed',
      error_count: validationResult.errorCount,
      warning_count: validationResult.warningCount,
      file_name: `${instituteCode}_${metricNumber}_${new Date().toISOString().split('T')[0]}.csv`,
      original_file_name: originalFileName,
      submitted_to_qmb: false,
      file_path: filePath,
      validation_result: validationResult
    };

    // Save to localStorage (in production, save to database)
    const submissions = this.getSubmissions();
    submissions.push(submission);
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(submissions));

    return submission;
  }

  /**
   * Get all submissions
   */
  public static getSubmissions(): Submission[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return [];
      
      const submissions = JSON.parse(stored) as Submission[];
      // Convert timestamp strings back to Date objects
      return submissions.map(sub => ({
        ...sub,
        timestamp: new Date(sub.timestamp),
        validation_result: sub.validation_result ? {
          ...sub.validation_result,
          timestamp: new Date(sub.validation_result.timestamp)
        } : undefined
      }));
    } catch {
      return [];
    }
  }

  /**
   * Get submissions by institute
   */
  public static getSubmissionsByInstitute(instituteCode: string): Submission[] {
    return this.getSubmissions().filter(sub => sub.institute_code === instituteCode);
  }

  /**
   * Get submissions by metric
   */
  public static getSubmissionsByMetric(metricNumber: string): Submission[] {
    return this.getSubmissions().filter(sub => sub.metric_number === metricNumber);
  }

  /**
   * Mark submission as submitted to QMB
   */
  public static markAsSubmittedToQMB(submissionId: string): boolean {
    try {
      const submissions = this.getSubmissions();
      const index = submissions.findIndex(sub => sub.id === submissionId);
      
      if (index === -1) return false;
      
      submissions[index].submitted_to_qmb = true;
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(submissions));
      
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Generate unique submission ID
   */
  private static generateSubmissionId(): string {
    return `SUB_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate error report in Excel format
   */
  public static generateErrorReport(
    submission: Submission,
    instituteCode: string,
    instituteName: string,
    metricNumber: string
  ): ErrorReport {
    const report: ErrorReport = {
      reportId: `ERR_${Date.now()}`,
      instituteCode,
      instituteName,
      metricNumber,
      generatedAt: new Date(),
      errors: submission.validation_result?.errors || [],
      summary: submission.validation_result?.summary || {
        totalRows: 0,
        validRows: 0,
        errorRows: 0,
        warningRows: 0,
        criticalErrors: [],
        commonErrors: {}
      },
      recommendations: this.generateRecommendations(submission.validation_result?.errors || [])
    };

    return report;
  }

  /**
   * Download error report as Excel file
   */
  public static downloadErrorReport(report: ErrorReport): void {
    // Create workbook
    const wb = XLSX.utils.book_new();

    // Summary sheet
    const summaryData = [
      ['NAAC AQAR Validation Error Report'],
      [''],
      ['Report ID', report.reportId],
      ['Institute Code', report.instituteCode],
      ['Institute Name', report.instituteName],
      ['Metric Number', report.metricNumber],
      ['Generated At', report.generatedAt.toLocaleString()],
      [''],
      ['Summary'],
      ['Total Rows', report.summary.totalRows],
      ['Valid Rows', report.summary.validRows],
      ['Error Rows', report.summary.errorRows],
      ['Warning Rows', report.summary.warningRows],
      [''],
      ['Common Errors'],
      ...Object.entries(report.summary.commonErrors).map(([error, count]) => [error, count])
    ];

    const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');

    // Errors sheet
    const errorHeaders = ['Row', 'Column', 'Value', 'Error', 'Rule', 'Severity'];
    const errorData = [
      errorHeaders,
      ...report.errors.map(error => [
        error.row,
        error.column,
        error.value,
        error.error,
        error.rule,
        error.severity
      ])
    ];

    const errorWs = XLSX.utils.aoa_to_sheet(errorData);
    XLSX.utils.book_append_sheet(wb, errorWs, 'Errors');

    // Recommendations sheet
    const recommendationData = [
      ['Recommendations'],
      [''],
      ...report.recommendations.map(rec => [rec])
    ];

    const recommendationWs = XLSX.utils.aoa_to_sheet(recommendationData);
    XLSX.utils.book_append_sheet(wb, recommendationWs, 'Recommendations');

    // Download file
    const fileName = `AQAR_Error_Report_${report.instituteCode}_${report.metricNumber}_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);
  }

  /**
   * Generate recommendations based on errors
   */
  private static generateRecommendations(errors: any[]): string[] {
    const recommendations: string[] = [];
    const errorTypes = new Set(errors.map(error => error.rule));

    errorTypes.forEach(errorType => {
      switch (errorType) {
        case 'required_field':
          recommendations.push('Ensure all required fields are filled. Check the template for mandatory columns.');
          break;
        case 'year_range':
          recommendations.push('Verify that all year entries are within the specified range (typically 2019-2024).');
          break;
        case 'numeric':
          recommendations.push('Check that numeric fields contain only numbers without any text or special characters.');
          break;
        case 'institute_code_choice':
          recommendations.push('Use only valid SIU institute codes. Refer to the institution list provided.');
          break;
        case 'date':
          recommendations.push('Ensure dates are in DD/MM/YYYY format and represent valid dates.');
          break;
        case 'word_limit':
          recommendations.push('Review text fields that exceed word limits and provide concise descriptions.');
          break;
        case 'exact_match':
          recommendations.push('Use only the specified values for choice fields. Check the template for valid options.');
          break;
        case 'url_pattern':
          recommendations.push('Ensure URLs are properly formatted and start with http:// or https://');
          break;
        case 'currency':
          recommendations.push('Enter currency amounts as numbers only, without currency symbols or commas.');
          break;
        default:
          recommendations.push(`Review and correct ${errorType} validation errors according to the field requirements.`);
      }
    });

    // Add general recommendations
    recommendations.push('');
    recommendations.push('General Recommendations:');
    recommendations.push('1. Download and use the latest CSV template for your metric');
    recommendations.push('2. Do not modify column headers in the template');
    recommendations.push('3. Ensure data consistency across all rows');
    recommendations.push('4. Validate your data before final submission');
    recommendations.push('5. Contact QMB team for clarification if needed: <EMAIL>');

    return recommendations;
  }

  /**
   * Get submission statistics
   */
  public static getSubmissionStatistics(): {
    totalSubmissions: number;
    passedValidations: number;
    failedValidations: number;
    submittedToQMB: number;
    byInstitute: { [key: string]: number };
    byMetric: { [key: string]: number };
    recentSubmissions: Submission[];
  } {
    const submissions = this.getSubmissions();

    const stats = {
      totalSubmissions: submissions.length,
      passedValidations: submissions.filter(sub => sub.validation_status === 'passed').length,
      failedValidations: submissions.filter(sub => sub.validation_status === 'failed').length,
      submittedToQMB: submissions.filter(sub => sub.submitted_to_qmb).length,
      byInstitute: {} as { [key: string]: number },
      byMetric: {} as { [key: string]: number },
      recentSubmissions: submissions
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 10)
    };

    // Count by institute
    submissions.forEach(sub => {
      stats.byInstitute[sub.institute_code] = (stats.byInstitute[sub.institute_code] || 0) + 1;
    });

    // Count by metric
    submissions.forEach(sub => {
      stats.byMetric[sub.metric_number] = (stats.byMetric[sub.metric_number] || 0) + 1;
    });

    return stats;
  }

  /**
   * Clear all submissions (for testing/admin purposes)
   */
  public static clearAllSubmissions(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }

  /**
   * Export submissions to Excel
   */
  public static exportSubmissionsToExcel(): void {
    const submissions = this.getSubmissions();
    
    const data = [
      ['Submission ID', 'Timestamp', 'Institute Code', 'Institute Name', 'Metric Number', 'Validation Status', 'Error Count', 'Warning Count', 'File Name', 'Submitted to QMB'],
      ...submissions.map(sub => [
        sub.id,
        sub.timestamp.toLocaleString(),
        sub.institute_code,
        sub.institute_name,
        sub.metric_number,
        sub.validation_status,
        sub.error_count,
        sub.warning_count,
        sub.original_file_name,
        sub.submitted_to_qmb ? 'Yes' : 'No'
      ])
    ];

    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(wb, ws, 'Submissions');

    const fileName = `AQAR_Submissions_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);
  }
}
