/**
 * NAAC AQAR Metrics Data
 * All 53 metrics as specified in requirements
 * Author: Dr. <PERSON>, Symbiosis International (Deemed University)
 */

import { Metric } from '@/types';

export const METRICS: Metric[] = [
  // Criteria 1: Curricular Aspects
  { metric_number: '1.1.1', metric_description: 'Curricula developed and implemented have relevance to the local, national, regional and global developmental needs', criteria_number: '1.1' },
  { metric_number: '1.1.1a', metric_description: 'Global developmental needs which the curricula address', criteria_number: '1.1' },
  { metric_number: '1.1.1b', metric_description: 'National developmental needs which the curricula address', criteria_number: '1.1' },
  { metric_number: '1.1.1c', metric_description: 'Regional developmental needs which the curricula address', criteria_number: '1.1' },
  { metric_number: '1.1.1d', metric_description: 'Local developmental needs which the curricula address', criteria_number: '1.1' },
  { metric_number: '1.1.3', metric_description: 'Percentage of students undertaking project work/field work/internships', criteria_number: '1.1' },
  { metric_number: '1.3.1', metric_description: 'Institution integrates crosscutting issues relevant to Professional Ethics, Gender, Human Values, Environment and Sustainability into the Curriculum', criteria_number: '1.3' },
  { metric_number: '1.3.1a', metric_description: 'Number of courses that include focus on gender issues during the last five years', criteria_number: '1.3' },
  { metric_number: '1.3.2a', metric_description: 'Number of value-added courses for imparting transferable and life skills offered during last five years', criteria_number: '1.3' },
  { metric_number: '1.3.2b', metric_description: 'Number of students enrolled in the courses under 1.3.2 above', criteria_number: '1.3' },
  { metric_number: '1.3.4', metric_description: 'Percentage of students undertaking field projects/internships/student projects', criteria_number: '1.3' },
  { metric_number: '1.4.1', metric_description: 'Institution obtains feedback on the syllabus and its transaction at the institution from the following stakeholders', criteria_number: '1.4' },
  { metric_number: '1.4.2', metric_description: 'Feedback process of the Institution may be classified as follows', criteria_number: '1.4' },

  // Criteria 2: Teaching-learning and Evaluation
  { metric_number: '2.2.1', metric_description: 'Student – Full time Teacher Ratio', criteria_number: '2.2' },
  { metric_number: '2.3.1', metric_description: 'Student centric methods, such as experiential learning, participative learning and problem solving methodologies are used for enhancing learning experiences', criteria_number: '2.3' },
  { metric_number: '2.3.2', metric_description: 'Teachers use ICT enabled tools for effective teaching-learning process', criteria_number: '2.3' },
  { metric_number: '2.3.3', metric_description: 'Ratio of students to mentor for academic and other related issues', criteria_number: '2.3' },
  { metric_number: '2.4.4', metric_description: 'Percentage of full time teachers against sanctioned posts during the last five years', criteria_number: '2.4' },
  { metric_number: '2.6.1', metric_description: 'Programme and course outcomes for all Programmes offered by the institution are stated and displayed on website and communicated to teachers and students', criteria_number: '2.6' },
  { metric_number: '2.6.2', metric_description: 'Attainment of Programme outcomes and course outcomes are evaluated by the institution', criteria_number: '2.6' },

  // Criteria 3: Research, Innovations and Extension
  { metric_number: '3.1.5', metric_description: 'Institution has the following facilities for research', criteria_number: '3.1' },
  { metric_number: '3.3.2', metric_description: 'Number of workshops/seminars conducted on Research Methodology, Intellectual Property Rights (IPR) and entrepreneurship during the last five years', criteria_number: '3.3' },
  { metric_number: '3.3.3', metric_description: 'Number of awards/recognitions received for extension activities from government/government recognised bodies during the last five years', criteria_number: '3.3' },
  { metric_number: '3.4.7', metric_description: 'Number of registered patents (published/not published) and patents (Granted) during the last five years', criteria_number: '3.4' },
  { metric_number: '3.6.1', metric_description: 'Extension activities are carried out in the neighborhood community, sensitizing students to social issues, for their holistic development, and impact thereof during the last five years', criteria_number: '3.6' },
  { metric_number: '3.6.2', metric_description: 'Number of awards and recognitions received for extension activities from government/government recognised bodies during the last five years', criteria_number: '3.6' },
  { metric_number: '3.6.3', metric_description: 'Number of extension and outreach programs conducted by the institution through NSS/NCC/Red cross/YRC etc., during the last five years', criteria_number: '3.6' },
  { metric_number: '3.6.4', metric_description: 'Average percentage of students participating in extension activities at 3.6.3 above during last five years', criteria_number: '3.6' },
  { metric_number: '3.7.1', metric_description: 'Number of Collaborative activities for research, Faculty exchange, Student exchange/internship during the last five years', criteria_number: '3.7' },

  // Criteria 4: Infrastructure and Learning Resources
  { metric_number: '4.1.1', metric_description: 'The Institution has adequate infrastructure and physical facilities for teaching-learning. viz., classrooms, laboratories, computing equipment etc.', criteria_number: '4.1' },
  { metric_number: '4.3.3', metric_description: 'Bandwidth of internet connection in the Institution', criteria_number: '4.3' },
  { metric_number: '4.3.5', metric_description: 'Institution has the following Facilities for e-content development', criteria_number: '4.3' },

  // Criteria 5: Student Support and Progression
  { metric_number: '5.1.2', metric_description: 'Number of capability enhancement and development schemes such as Soft skill development, Remedial coaching, Language lab, Bridge courses, Yoga, Meditation, Personal Counselling and Mentoring etc., for students', criteria_number: '5.1' },
  { metric_number: '5.1.3a', metric_description: 'Number of students benefitted by guidance for competitive examinations and career counselling offered by the Institution during the last five years', criteria_number: '5.1' },
  { metric_number: '5.1.3b', metric_description: 'Number of students who have passed in the qualifying examination conducted by national/state/government or other recognized bodies during the last five years', criteria_number: '5.1' },
  { metric_number: '5.2.1', metric_description: 'Average percentage of placement of outgoing students during the last five years', criteria_number: '5.2' },
  { metric_number: '5.2.2', metric_description: 'Average percentage of students progressing to higher education during the last five years', criteria_number: '5.2' },
  { metric_number: '5.2.3', metric_description: 'Average percentage of students qualifying in state/national/international level examinations during the last five years', criteria_number: '5.2' },
  { metric_number: '5.3.1', metric_description: 'Number of awards/medals won by students for outstanding performance in sports/cultural activities at inter-university/state/national/international level during the last five years', criteria_number: '5.3' },
  { metric_number: '5.3.2', metric_description: 'Institution facilitates students representation and engagement in various administrative, co-curricular and extracurricular activities', criteria_number: '5.3' },
  { metric_number: '5.3.3', metric_description: 'Average number of sports and cultural programs in which students of the Institution participated during last five years', criteria_number: '5.3' },
  { metric_number: '5.4.1', metric_description: 'There is a registered Alumni Association that contributes significantly to the development of the institution through financial and/or other support services', criteria_number: '5.4' },

  // Criteria 6: Governance, Leadership and Management
  { metric_number: '6.3.3', metric_description: 'Average number of professional development/administrative training programs organized by the institution for teaching and non-teaching staff during the last five years', criteria_number: '6.3' },
  { metric_number: '6.3.4', metric_description: 'Average percentage of teachers undergoing online/face-to-face Faculty Development Programmes (FDP) during the last five years', criteria_number: '6.3' },
  { metric_number: '6.5.2', metric_description: 'The institution reviews its teaching learning process, structures & methodologies of operations and learning outcomes at periodic intervals through IQAC set up as per norms and recorded the incremental improvement in various activities', criteria_number: '6.5' },

  // Criteria 7: Institutional Values and Best Practices
  { metric_number: '7.1.1', metric_description: 'Measures initiated by the Institution for the promotion of gender equity during the last five years', criteria_number: '7.1' },
  { metric_number: '7.1.8', metric_description: 'Average percentage of expenditure for infrastructure augmentation excluding salary during the last five years', criteria_number: '7.1' },
  { metric_number: '7.1.9', metric_description: 'Sensitization of students and employees of the Institution to the constitutional obligations: values, rights, duties and responsibilities of citizens', criteria_number: '7.1' },
  { metric_number: '7.1.10', metric_description: 'The Institution has a prescribed code of conduct for students, teachers, administrators and other staff and conducts periodic programmes in this regard', criteria_number: '7.1' },
  { metric_number: '7.1.11', metric_description: 'Institution celebrates/organizes national and international commemorative days, events and festivals', criteria_number: '7.1' },
  { metric_number: '7.2.1', metric_description: 'Describe two best practices successfully implemented by the Institution as per NAAC format provided in the Manual', criteria_number: '7.2' },
  { metric_number: '7.3.1', metric_description: 'Portray the performance of the Institution in one area distinctive to its priority and thrust within a maximum of 500 words', criteria_number: '7.3' }
];

// Helper functions
export const getMetricByNumber = (metricNumber: string): Metric | undefined => {
  return METRICS.find(metric => metric.metric_number === metricNumber);
};

export const getMetricsByCriteria = (criteriaNumber: string): Metric[] => {
  return METRICS.filter(metric => metric.criteria_number === criteriaNumber);
};

export const getAllCriteria = (): string[] => {
  return [...new Set(METRICS.map(metric => metric.criteria_number))].sort();
};

export const getMetricsByKeyword = (keyword: string): Metric[] => {
  const searchTerm = keyword.toLowerCase();
  return METRICS.filter(metric => 
    metric.metric_description.toLowerCase().includes(searchTerm) ||
    metric.metric_number.toLowerCase().includes(searchTerm)
  );
};
