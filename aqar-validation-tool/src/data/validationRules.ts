/**
 * NAAC AQAR Validation Rules
 * Comprehensive validation rules for all metrics
 * Author: Dr<PERSON>, Symbiosis International (Deemed University)
 */

import { ValidationRule } from '@/types';

export const VALIDATION_RULES: ValidationRule[] = [
  // Metric 1.1.1 - Curricula developed and implemented
  {
    id: 'rule_001',
    metric_number: '1.1.1',
    metric_description: 'Curricula developed and implemented have relevance to the local, national, regional and global developmental needs',
    column_name: 'Programme_Name',
    is_required: true,
    data_type: 'text',
    validation_type: 'required_field',
    validation_params: '',
    error_message: 'Programme Name is required'
  },
  {
    id: 'rule_002',
    metric_number: '1.1.1',
    metric_description: 'Curricula developed and implemented have relevance to the local, national, regional and global developmental needs',
    column_name: 'Academic_Year',
    is_required: true,
    data_type: 'text',
    validation_type: 'year_range',
    validation_params: '2019-2024',
    error_message: 'Academic Year must be between 2019-2024'
  },
  {
    id: 'rule_003',
    metric_number: '1.1.1',
    metric_description: 'Curricula developed and implemented have relevance to the local, national, regional and global developmental needs',
    column_name: 'Institute_Code',
    is_required: true,
    data_type: 'text',
    validation_type: 'institute_code_choice',
    validation_params: '',
    reference_table: 'institutions',
    error_message: 'Invalid Institute Code'
  },
  {
    id: 'rule_004',
    metric_number: '1.1.1',
    metric_description: 'Curricula developed and implemented have relevance to the local, national, regional and global developmental needs',
    column_name: 'Institute_Name',
    is_required: true,
    data_type: 'text',
    validation_type: 'institute_name_choice',
    validation_params: '',
    reference_table: 'institutions',
    error_message: 'Invalid Institute Name'
  },
  {
    id: 'rule_005',
    metric_number: '1.1.1',
    metric_description: 'Curricula developed and implemented have relevance to the local, national, regional and global developmental needs',
    column_name: 'Faculty',
    is_required: true,
    data_type: 'text',
    validation_type: 'faculty_choice',
    validation_params: 'LAW,MANAGEMENT,ENGINEERING,COMPUTER_SCIENCE,MEDIA,DESIGN,HEALTH_SCIENCES,TELECOM,OPERATIONS,STATISTICS,ECONOMICS,FINANCE,LIBERAL_ARTS,CULINARY,GEOINFORMATICS,INTERNATIONAL_STUDIES,SPORTS,PHOTOGRAPHY,ARCHITECTURE,BIOLOGICAL_SCIENCES,PERFORMING_ARTS,SKILLS,DISTANCE_LEARNING,INFORMATION_TECHNOLOGY,RESEARCH,SCHOOL,CORPORATE_EDUCATION,LANGUAGES',
    error_message: 'Invalid Faculty selection'
  },

  // Metric 1.1.1a - Global developmental needs
  {
    id: 'rule_006',
    metric_number: '1.1.1a',
    metric_description: 'Global developmental needs which the curricula address',
    column_name: 'Global_Need_Description',
    is_required: true,
    data_type: 'text',
    validation_type: 'word_limit',
    validation_params: '500',
    error_message: 'Global Need Description must not exceed 500 words'
  },
  {
    id: 'rule_007',
    metric_number: '1.1.1a',
    metric_description: 'Global developmental needs which the curricula address',
    column_name: 'Evidence_Document',
    is_required: true,
    data_type: 'file',
    validation_type: 'pdf_file',
    validation_params: '',
    error_message: 'Evidence document must be a PDF file'
  },

  // Metric 1.1.1b - National developmental needs
  {
    id: 'rule_008',
    metric_number: '1.1.1b',
    metric_description: 'National developmental needs which the curricula address',
    column_name: 'National_Need_Description',
    is_required: true,
    data_type: 'text',
    validation_type: 'word_limit',
    validation_params: '500',
    error_message: 'National Need Description must not exceed 500 words'
  },

  // Metric 1.1.3 - Percentage of students undertaking project work
  {
    id: 'rule_009',
    metric_number: '1.1.3',
    metric_description: 'Percentage of students undertaking project work/field work/internships',
    column_name: 'Total_Students',
    is_required: true,
    data_type: 'numeric',
    validation_type: 'numeric',
    validation_params: 'min:1,max:10000',
    error_message: 'Total Students must be a number between 1 and 10000'
  },
  {
    id: 'rule_010',
    metric_number: '1.1.3',
    metric_description: 'Percentage of students undertaking project work/field work/internships',
    column_name: 'Students_With_Projects',
    is_required: true,
    data_type: 'numeric',
    validation_type: 'numeric',
    validation_params: 'min:0,max:10000',
    error_message: 'Students with Projects must be a number between 0 and 10000'
  },

  // Metric 1.3.1 - Integration of crosscutting issues
  {
    id: 'rule_011',
    metric_number: '1.3.1',
    metric_description: 'Institution integrates crosscutting issues relevant to Professional Ethics, Gender, Human Values, Environment and Sustainability',
    column_name: 'Professional_Ethics',
    is_required: true,
    data_type: 'text',
    validation_type: 'yes_no_choice',
    validation_params: 'Yes,No',
    error_message: 'Professional Ethics must be Yes or No'
  },
  {
    id: 'rule_012',
    metric_number: '1.3.1',
    metric_description: 'Institution integrates crosscutting issues relevant to Professional Ethics, Gender, Human Values, Environment and Sustainability',
    column_name: 'Gender_Issues',
    is_required: true,
    data_type: 'text',
    validation_type: 'yes_no_choice',
    validation_params: 'Yes,No',
    error_message: 'Gender Issues must be Yes or No'
  },

  // Metric 2.2.1 - Student-Teacher Ratio
  {
    id: 'rule_013',
    metric_number: '2.2.1',
    metric_description: 'Student – Full time Teacher Ratio',
    column_name: 'Total_Students_Enrolled',
    is_required: true,
    data_type: 'numeric',
    validation_type: 'numeric',
    validation_params: 'min:1,max:50000',
    error_message: 'Total Students Enrolled must be between 1 and 50000'
  },
  {
    id: 'rule_014',
    metric_number: '2.2.1',
    metric_description: 'Student – Full time Teacher Ratio',
    column_name: 'Total_Fulltime_Teachers',
    is_required: true,
    data_type: 'numeric',
    validation_type: 'numeric',
    validation_params: 'min:1,max:5000',
    error_message: 'Total Fulltime Teachers must be between 1 and 5000'
  },

  // Metric 3.3.2 - Workshops/seminars on Research Methodology
  {
    id: 'rule_015',
    metric_number: '3.3.2',
    metric_description: 'Number of workshops/seminars conducted on Research Methodology, Intellectual Property Rights (IPR) and entrepreneurship',
    column_name: 'Workshop_Title',
    is_required: true,
    data_type: 'text',
    validation_type: 'text_length',
    validation_params: 'min:10,max:200',
    error_message: 'Workshop Title must be between 10 and 200 characters'
  },
  {
    id: 'rule_016',
    metric_number: '3.3.2',
    metric_description: 'Number of workshops/seminars conducted on Research Methodology, Intellectual Property Rights (IPR) and entrepreneurship',
    column_name: 'Date_Conducted',
    is_required: true,
    data_type: 'date',
    validation_type: 'date',
    validation_params: 'format:DD/MM/YYYY,range:01/01/2019-31/12/2024',
    error_message: 'Date must be in DD/MM/YYYY format between 01/01/2019 and 31/12/2024'
  },

  // Metric 4.3.3 - Bandwidth of internet connection
  {
    id: 'rule_017',
    metric_number: '4.3.3',
    metric_description: 'Bandwidth of internet connection in the Institution',
    column_name: 'Bandwidth_Mbps',
    is_required: true,
    data_type: 'numeric',
    validation_type: 'numeric',
    validation_params: 'min:1,max:10000',
    error_message: 'Bandwidth must be between 1 and 10000 Mbps'
  },
  {
    id: 'rule_018',
    metric_number: '4.3.3',
    metric_description: 'Bandwidth of internet connection in the Institution',
    column_name: 'Service_Provider',
    is_required: true,
    data_type: 'text',
    validation_type: 'required_field',
    validation_params: '',
    error_message: 'Service Provider is required'
  },

  // Metric 5.2.1 - Average percentage of placement
  {
    id: 'rule_019',
    metric_number: '5.2.1',
    metric_description: 'Average percentage of placement of outgoing students during the last five years',
    column_name: 'Students_Eligible_For_Placement',
    is_required: true,
    data_type: 'numeric',
    validation_type: 'numeric',
    validation_params: 'min:0,max:10000',
    error_message: 'Students Eligible for Placement must be between 0 and 10000'
  },
  {
    id: 'rule_020',
    metric_number: '5.2.1',
    metric_description: 'Average percentage of placement of outgoing students during the last five years',
    column_name: 'Students_Placed',
    is_required: true,
    data_type: 'numeric',
    validation_type: 'numeric',
    validation_params: 'min:0,max:10000',
    error_message: 'Students Placed must be between 0 and 10000'
  },

  // Metric 6.3.4 - Teachers undergoing FDP
  {
    id: 'rule_021',
    metric_number: '6.3.4',
    metric_description: 'Average percentage of teachers undergoing online/face-to-face Faculty Development Programmes (FDP)',
    column_name: 'FDP_Type',
    is_required: true,
    data_type: 'text',
    validation_type: 'exact_match',
    validation_params: 'Online,Face-to-Face,Hybrid',
    error_message: 'FDP Type must be Online, Face-to-Face, or Hybrid'
  },
  {
    id: 'rule_022',
    metric_number: '6.3.4',
    metric_description: 'Average percentage of teachers undergoing online/face-to-face Faculty Development Programmes (FDP)',
    column_name: 'Duration_Hours',
    is_required: true,
    data_type: 'numeric',
    validation_type: 'numeric',
    validation_params: 'min:1,max:720',
    error_message: 'Duration must be between 1 and 720 hours'
  },

  // Metric 7.1.8 - Infrastructure expenditure
  {
    id: 'rule_023',
    metric_number: '7.1.8',
    metric_description: 'Average percentage of expenditure for infrastructure augmentation excluding salary',
    column_name: 'Infrastructure_Expenditure',
    is_required: true,
    data_type: 'currency',
    validation_type: 'currency',
    validation_params: 'min:0,max:100000000',
    error_message: 'Infrastructure Expenditure must be a valid currency amount'
  },
  {
    id: 'rule_024',
    metric_number: '7.1.8',
    metric_description: 'Average percentage of expenditure for infrastructure augmentation excluding salary',
    column_name: 'Total_Expenditure',
    is_required: true,
    data_type: 'currency',
    validation_type: 'currency',
    validation_params: 'min:1,max:1000000000',
    error_message: 'Total Expenditure must be a valid currency amount'
  },

  // Additional validation rules for comprehensive coverage

  // Metric 1.3.1a - Gender focus courses
  {
    id: 'rule_025',
    metric_number: '1.3.1a',
    metric_description: 'Number of courses that include focus on gender issues during the last five years',
    column_name: 'Course_Name',
    is_required: true,
    data_type: 'text',
    validation_type: 'required_field',
    validation_params: '',
    error_message: 'Course Name is required'
  },
  {
    id: 'rule_026',
    metric_number: '1.3.1a',
    metric_description: 'Number of courses that include focus on gender issues during the last five years',
    column_name: 'Gender_Focus_Description',
    is_required: true,
    data_type: 'text',
    validation_type: 'word_limit',
    validation_params: '200',
    error_message: 'Gender Focus Description must not exceed 200 words'
  },

  // Metric 1.3.2a - Value-added courses
  {
    id: 'rule_027',
    metric_number: '1.3.2a',
    metric_description: 'Number of value-added courses for imparting transferable and life skills',
    column_name: 'Course_Type',
    is_required: true,
    data_type: 'text',
    validation_type: 'exact_match',
    validation_params: 'Transferable Skills,Life Skills,Professional Skills,Communication Skills,Leadership Skills',
    error_message: 'Course Type must be one of the specified skill categories'
  },
  {
    id: 'rule_028',
    metric_number: '1.3.2a',
    metric_description: 'Number of value-added courses for imparting transferable and life skills',
    column_name: 'Duration_Hours',
    is_required: true,
    data_type: 'numeric',
    validation_type: 'numeric',
    validation_params: 'min:1,max:500',
    error_message: 'Duration must be between 1 and 500 hours'
  },

  // Metric 1.3.2b - Students enrolled in value-added courses
  {
    id: 'rule_029',
    metric_number: '1.3.2b',
    metric_description: 'Number of students enrolled in the courses under 1.3.2 above',
    column_name: 'Students_Enrolled',
    is_required: true,
    data_type: 'numeric',
    validation_type: 'numeric',
    validation_params: 'min:1,max:1000',
    error_message: 'Students Enrolled must be between 1 and 1000'
  },

  // Metric 1.3.4 - Field projects/internships
  {
    id: 'rule_030',
    metric_number: '1.3.4',
    metric_description: 'Percentage of students undertaking field projects/internships/student projects',
    column_name: 'Project_Type',
    is_required: true,
    data_type: 'text',
    validation_type: 'exact_match',
    validation_params: 'Field Project,Internship,Student Project,Research Project',
    error_message: 'Project Type must be Field Project, Internship, Student Project, or Research Project'
  },

  // Metric 1.4.1 - Feedback from stakeholders
  {
    id: 'rule_031',
    metric_number: '1.4.1',
    metric_description: 'Institution obtains feedback on the syllabus and its transaction from stakeholders',
    column_name: 'Stakeholder_Type',
    is_required: true,
    data_type: 'text',
    validation_type: 'exact_match',
    validation_params: 'Students,Teachers,Employers,Alumni,Parents',
    error_message: 'Stakeholder Type must be Students, Teachers, Employers, Alumni, or Parents'
  },
  {
    id: 'rule_032',
    metric_number: '1.4.1',
    metric_description: 'Institution obtains feedback on the syllabus and its transaction from stakeholders',
    column_name: 'Feedback_Method',
    is_required: true,
    data_type: 'text',
    validation_type: 'exact_match',
    validation_params: 'Online Survey,Physical Form,Interview,Focus Group,Email',
    error_message: 'Feedback Method must be one of the specified methods'
  },

  // Metric 1.4.2 - Feedback process classification
  {
    id: 'rule_033',
    metric_number: '1.4.2',
    metric_description: 'Feedback process of the Institution classification',
    column_name: 'Process_Type',
    is_required: true,
    data_type: 'text',
    validation_type: 'exact_match',
    validation_params: 'A. Feedback collected, analysed and action taken and feedback available on website,B. Feedback collected, analysed and action has been taken,C. Feedback collected and analysed,D. Feedback collected,E. Feedback not obtained',
    error_message: 'Process Type must be one of the specified feedback process classifications'
  },

  // Metric 2.3.1 - Student centric methods
  {
    id: 'rule_034',
    metric_number: '2.3.1',
    metric_description: 'Student centric methods for enhancing learning experiences',
    column_name: 'Teaching_Method',
    is_required: true,
    data_type: 'text',
    validation_type: 'contains_text',
    validation_params: 'experiential,participative,problem solving,case study,simulation,role play',
    error_message: 'Teaching Method must contain student-centric learning approaches'
  },

  // Metric 2.3.2 - ICT enabled tools
  {
    id: 'rule_035',
    metric_number: '2.3.2',
    metric_description: 'Teachers use ICT enabled tools for effective teaching-learning process',
    column_name: 'ICT_Tool',
    is_required: true,
    data_type: 'text',
    validation_type: 'exact_match',
    validation_params: 'LMS,Video Conferencing,Interactive Whiteboard,Educational Software,Online Assessment,Virtual Lab,Mobile App,E-books',
    error_message: 'ICT Tool must be one of the specified technology tools'
  },

  // Metric 2.3.3 - Student to mentor ratio
  {
    id: 'rule_036',
    metric_number: '2.3.3',
    metric_description: 'Ratio of students to mentor for academic and other related issues',
    column_name: 'Total_Mentors',
    is_required: true,
    data_type: 'numeric',
    validation_type: 'numeric',
    validation_params: 'min:1,max:500',
    error_message: 'Total Mentors must be between 1 and 500'
  },

  // Metric 2.4.4 - Full time teachers percentage
  {
    id: 'rule_037',
    metric_number: '2.4.4',
    metric_description: 'Percentage of full time teachers against sanctioned posts during the last five years',
    column_name: 'Sanctioned_Posts',
    is_required: true,
    data_type: 'numeric',
    validation_type: 'numeric',
    validation_params: 'min:1,max:1000',
    error_message: 'Sanctioned Posts must be between 1 and 1000'
  },

  // Metric 2.6.1 - Programme and course outcomes
  {
    id: 'rule_038',
    metric_number: '2.6.1',
    metric_description: 'Programme and course outcomes displayed on website and communicated',
    column_name: 'Website_URL',
    is_required: true,
    data_type: 'url',
    validation_type: 'url_pattern',
    validation_params: '',
    error_message: 'Website URL must be a valid URL'
  },
  {
    id: 'rule_039',
    metric_number: '2.6.1',
    metric_description: 'Programme and course outcomes displayed on website and communicated',
    column_name: 'Communication_Method',
    is_required: true,
    data_type: 'text',
    validation_type: 'exact_match',
    validation_params: 'Website,Student Handbook,Orientation Program,Course Syllabus,Notice Board',
    error_message: 'Communication Method must be one of the specified methods'
  },

  // Metric 2.6.2 - Attainment evaluation
  {
    id: 'rule_040',
    metric_number: '2.6.2',
    metric_description: 'Attainment of Programme outcomes and course outcomes are evaluated',
    column_name: 'Evaluation_Method',
    is_required: true,
    data_type: 'text',
    validation_type: 'exact_match',
    validation_params: 'Direct Assessment,Indirect Assessment,Course Exit Survey,Alumni Survey,Employer Survey',
    error_message: 'Evaluation Method must be one of the specified assessment methods'
  },

  // Common validation rules for all metrics
  {
    id: 'rule_041',
    metric_number: 'ALL',
    metric_description: 'Common validation for all metrics',
    column_name: 'Remarks',
    is_required: false,
    data_type: 'text',
    validation_type: 'word_limit',
    validation_params: '1000',
    error_message: 'Remarks must not exceed 1000 words'
  },
  {
    id: 'rule_042',
    metric_number: 'ALL',
    metric_description: 'Common validation for all metrics',
    column_name: 'Supporting_Document_URL',
    is_required: false,
    data_type: 'url',
    validation_type: 'url_pattern',
    validation_params: '',
    error_message: 'Supporting Document URL must be a valid URL'
  }
];

// Helper functions
export const getValidationRulesByMetric = (metricNumber: string): ValidationRule[] => {
  return VALIDATION_RULES.filter(rule => 
    rule.metric_number === metricNumber || rule.metric_number === 'ALL'
  );
};

export const getValidationRuleById = (id: string): ValidationRule | undefined => {
  return VALIDATION_RULES.find(rule => rule.id === id);
};

export const getValidationRulesByColumn = (columnName: string): ValidationRule[] => {
  return VALIDATION_RULES.filter(rule => rule.column_name === columnName);
};

export const getRequiredColumns = (metricNumber: string): string[] => {
  return getValidationRulesByMetric(metricNumber)
    .filter(rule => rule.is_required)
    .map(rule => rule.column_name);
};

export const getAllValidationTypes = (): string[] => {
  return [...new Set(VALIDATION_RULES.map(rule => rule.validation_type))];
};
