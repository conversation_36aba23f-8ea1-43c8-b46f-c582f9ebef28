'use client';

import React, { useState, useCallback } from 'react';
import { Upload, Download, CheckCircle, XCircle, AlertTriangle, FileText, Building2, Target } from 'lucide-react';
import { ValidationFormData, ValidationResult, CSVData } from '@/types';
import { INSTITUTIONS } from '@/data/institutions';
import { METRICS } from '@/data/metrics';
import { ValidationEngine } from '@/lib/validationEngine';
import { CSVProcessor } from '@/utils/csvProcessor';
import { TemplateGenerator } from '@/lib/templateGenerator';

export const AQARValidationTool: React.FC = () => {
  const [formData, setFormData] = useState<ValidationFormData>({
    instituteCode: '',
    metricNumber: '',
    csvFile: null
  });
  
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [csvData, setCsvData] = useState<CSVData | null>(null);
  const [error, setError] = useState<string>('');

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validation = CSVProcessor.validateCSVFile(file);
    if (!validation.isValid) {
      setError(validation.error || 'Invalid file');
      return;
    }

    setFormData(prev => ({ ...prev, csvFile: file }));
    setError('');
  }, []);

  const handleValidation = useCallback(async () => {
    if (!formData.csvFile || !formData.instituteCode || !formData.metricNumber) {
      setError('Please fill all required fields and upload a CSV file');
      return;
    }

    setIsValidating(true);
    setError('');

    try {
      // Parse CSV file
      const parsedData = await CSVProcessor.parseCSVFile(formData.csvFile);
      setCsvData(parsedData);

      // Validate CSV data
      const validationEngine = new ValidationEngine();
      const result = await validationEngine.validateCSV(
        parsedData,
        formData.metricNumber,
        formData.instituteCode
      );

      setValidationResult(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Validation failed');
    } finally {
      setIsValidating(false);
    }
  }, [formData]);

  const handleDownloadTemplate = useCallback(() => {
    if (!formData.metricNumber) {
      setError('Please select a metric first');
      return;
    }

    try {
      TemplateGenerator.downloadTemplate(formData.metricNumber);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to download template');
    }
  }, [formData.metricNumber]);

  const handleSubmitToQMB = useCallback(() => {
    if (!validationResult?.isValid) {
      setError('Cannot submit file with validation errors');
      return;
    }

    // TODO: Implement submission to QMB
    alert('File submitted to QMB successfully!');
  }, [validationResult]);

  const selectedInstitution = INSTITUTIONS.find(inst => inst.institute_code === formData.instituteCode);
  const selectedMetric = METRICS.find(metric => metric.metric_number === formData.metricNumber);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-red-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b-4 border-red-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                NAAC AQAR CSV Validation Tool
              </h1>
              <p className="text-lg text-gray-600 mt-2">
                Symbiosis International University - Quality Assurance
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
                <FileText className="w-6 h-6 text-white" />
              </div>
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                <Building2 className="w-6 h-6 text-white" />
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Form Section */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">
                Validation Form
              </h2>

              {error && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center">
                    <XCircle className="w-5 h-5 text-red-500 mr-2" />
                    <span className="text-red-700">{error}</span>
                  </div>
                </div>
              )}

              <div className="space-y-6">
                {/* Institution Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Building2 className="w-4 h-4 inline mr-1" />
                    Select Your Institution *
                  </label>
                  <select
                    value={formData.instituteCode}
                    onChange={(e) => setFormData(prev => ({ ...prev, instituteCode: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Choose Institution...</option>
                    {INSTITUTIONS.map(inst => (
                      <option key={inst.institute_code} value={inst.institute_code}>
                        {inst.institute_code} - {inst.institute_short_name} - {inst.institute_name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Metric Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Target className="w-4 h-4 inline mr-1" />
                    Select Metric *
                  </label>
                  <select
                    value={formData.metricNumber}
                    onChange={(e) => setFormData(prev => ({ ...prev, metricNumber: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="">Choose Metric...</option>
                    {METRICS.map(metric => (
                      <option key={metric.metric_number} value={metric.metric_number}>
                        {metric.metric_number} - {metric.metric_description}
                      </option>
                    ))}
                  </select>
                </div>

                {/* CSV Upload */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Upload className="w-4 h-4 inline mr-1" />
                    Upload CSV File *
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                    <input
                      type="file"
                      accept=".csv"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="csv-upload"
                    />
                    <label htmlFor="csv-upload" className="cursor-pointer">
                      <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">
                        {formData.csvFile ? formData.csvFile.name : 'Click to upload CSV file'}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        Maximum file size: 10MB
                      </p>
                    </label>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-4">
                  <button
                    onClick={handleValidation}
                    disabled={!formData.csvFile || !formData.instituteCode || !formData.metricNumber || isValidating}
                    className="flex-1 min-w-[200px] bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                  >
                    {isValidating ? 'Validating...' : 'Validate CSV'}
                  </button>
                  
                  <button
                    onClick={handleSubmitToQMB}
                    disabled={!validationResult?.isValid}
                    className="flex-1 min-w-[200px] bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                  >
                    Submit to QMB
                  </button>
                  
                  <button
                    onClick={handleDownloadTemplate}
                    disabled={!formData.metricNumber}
                    className="flex-1 min-w-[200px] bg-orange-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-orange-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                  >
                    <Download className="w-4 h-4 inline mr-2" />
                    Download Template
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Info Panel */}
          <div className="space-y-6">
            {/* Selected Institution Info */}
            {selectedInstitution && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Selected Institution
                </h3>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Code:</span> {selectedInstitution.institute_code}</p>
                  <p><span className="font-medium">Name:</span> {selectedInstitution.institute_name}</p>
                  <p><span className="font-medium">Faculty:</span> {selectedInstitution.faculty}</p>
                  <p><span className="font-medium">City:</span> {selectedInstitution.city}</p>
                </div>
              </div>
            )}

            {/* Selected Metric Info */}
            {selectedMetric && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Selected Metric
                </h3>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Number:</span> {selectedMetric.metric_number}</p>
                  <p><span className="font-medium">Criteria:</span> {selectedMetric.criteria_number}</p>
                  <p className="text-gray-600">{selectedMetric.metric_description}</p>
                </div>
              </div>
            )}

            {/* CSV Info */}
            {csvData && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  CSV File Info
                </h3>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">File:</span> {csvData.fileName}</p>
                  <p><span className="font-medium">Size:</span> {(csvData.fileSize / 1024).toFixed(1)} KB</p>
                  <p><span className="font-medium">Rows:</span> {csvData.rowCount}</p>
                  <p><span className="font-medium">Columns:</span> {csvData.headers.length}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Validation Results */}
        {validationResult && (
          <div className="mt-8">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-semibold text-gray-900">
                  Validation Results
                </h2>
                <div className="flex items-center">
                  {validationResult.isValid ? (
                    <div className="flex items-center text-green-600">
                      <CheckCircle className="w-6 h-6 mr-2" />
                      <span className="font-medium">Validation Passed</span>
                    </div>
                  ) : (
                    <div className="flex items-center text-red-600">
                      <XCircle className="w-6 h-6 mr-2" />
                      <span className="font-medium">Validation Failed</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Summary */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{validationResult.processedRows}</div>
                  <div className="text-sm text-blue-800">Total Rows</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{validationResult.summary.validRows}</div>
                  <div className="text-sm text-green-800">Valid Rows</div>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{validationResult.errorCount}</div>
                  <div className="text-sm text-red-800">Errors</div>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{validationResult.warningCount}</div>
                  <div className="text-sm text-yellow-800">Warnings</div>
                </div>
              </div>

              {/* Error Details */}
              {validationResult.errors.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Error Details
                  </h3>
                  <div className="max-h-96 overflow-y-auto">
                    <div className="space-y-2">
                      {validationResult.errors.slice(0, 50).map((error, index) => (
                        <div
                          key={index}
                          className={`p-3 rounded-lg border-l-4 ${
                            error.severity === 'error'
                              ? 'bg-red-50 border-red-400'
                              : 'bg-yellow-50 border-yellow-400'
                          }`}
                        >
                          <div className="flex items-start">
                            {error.severity === 'error' ? (
                              <XCircle className="w-5 h-5 text-red-500 mr-2 mt-0.5" />
                            ) : (
                              <AlertTriangle className="w-5 h-5 text-yellow-500 mr-2 mt-0.5" />
                            )}
                            <div className="flex-1">
                              <p className="font-medium text-gray-900">
                                Row {error.row}, Column "{error.column}"
                              </p>
                              <p className="text-sm text-gray-600">{error.error}</p>
                              <p className="text-xs text-gray-500 mt-1">
                                Value: "{error.value}" | Rule: {error.rule}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                      {validationResult.errors.length > 50 && (
                        <div className="text-center py-4 text-gray-500">
                          ... and {validationResult.errors.length - 50} more errors
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </main>
    </div>
  );
};
