# NAAC AQAR Validation Tool - Local Environment Configuration
# Author: Dr<PERSON> <PERSON><PERSON>, Symbiosis International (Deemed University)

# Application Configuration
NEXT_PUBLIC_APP_NAME="NAAC AQAR Validation Tool"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_ORGANIZATION="Symbiosis International University"
NEXT_PUBLIC_CONTACT_EMAIL="<EMAIL>"
NEXT_PUBLIC_SUPPORT_EMAIL="<EMAIL>"

# API Configuration
NEXT_PUBLIC_API_BASE_URL="http://localhost:3000/api"
API_TIMEOUT=30000

# File Upload Configuration
NEXT_PUBLIC_MAX_FILE_SIZE=10485760
NEXT_PUBLIC_ALLOWED_FILE_TYPES=".csv"
UPLOAD_DIR="./uploads"

# Validation Configuration
VALIDATION_TIMEOUT=60000
MAX_VALIDATION_ERRORS=1000
ENABLE_DETAILED_LOGGING=true

# Database Configuration (for future use)
# DATABASE_URL="postgresql://username:password@localhost:5432/aqar_validation"
# DATABASE_POOL_SIZE=10

# Email Configuration (for future use)
# SMTP_HOST="smtp.siu.edu.in"
# SMTP_PORT=587
# SMTP_USER="<EMAIL>"
# SMTP_PASS="your-password"
# <AUTHOR> <EMAIL>"

# Security Configuration
SESSION_SECRET="your-super-secret-session-key-change-in-production"
ENCRYPTION_KEY="your-32-character-encryption-key-here"

# Feature Flags
ENABLE_BULK_UPLOAD=true
ENABLE_TEMPLATE_DOWNLOAD=true
ENABLE_ERROR_REPORTING=true
ENABLE_STATISTICS=true
ENABLE_ADMIN_PANEL=false

# Logging Configuration
LOG_LEVEL="info"
LOG_FILE="./logs/aqar-validation.log"
ENABLE_FILE_LOGGING=true

# Performance Configuration
ENABLE_CACHING=true
CACHE_TTL=3600
ENABLE_COMPRESSION=true

# Development Configuration
NODE_ENV="development"
NEXT_PUBLIC_DEBUG_MODE=true
