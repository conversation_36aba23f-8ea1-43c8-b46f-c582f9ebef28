(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1813:(e,t,i)=>{Promise.resolve().then(i.bind(i,8641))},8641:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>w});var r=i(5155),a=i(2115),n=i(7434),s=i(3227),o=i(4861),c=i(6785),l=i(9869),d=i(1788),m=i(646),u=i(1243);let _=[{institute_code:"101",institute_short_name:"SLS-P",institute_name:"Symbiosis Law School, Pune",faculty:"LAW",city:"Pune"},{institute_code:"102",institute_short_name:"SLS-N",institute_name:"Symbiosis Law School, Noida",faculty:"LAW",city:"Noida"},{institute_code:"103",institute_short_name:"SLS-H",institute_name:"Symbiosis Law School, Hyderabad",faculty:"LAW",city:"Hyderabad"},{institute_code:"104",institute_short_name:"SLS-NG",institute_name:"Symbiosis Law School, Nagpur",faculty:"LAW",city:"Nagpur"},{institute_code:"201",institute_short_name:"SIBM-P",institute_name:"Symbiosis Institute of Business Management, Pune",faculty:"MANAGEMENT",city:"Pune"},{institute_code:"202",institute_short_name:"SIIB",institute_name:"Symbiosis Institute of International Business",faculty:"MANAGEMENT",city:"Pune"},{institute_code:"203",institute_short_name:"SIBM-B",institute_name:"Symbiosis Institute of Business Management, Bengaluru",faculty:"MANAGEMENT",city:"Bengaluru"},{institute_code:"204",institute_short_name:"SIBM-H",institute_name:"Symbiosis Institute of Business Management, Hyderabad",faculty:"MANAGEMENT",city:"Hyderabad"},{institute_code:"205",institute_short_name:"SIBM-N",institute_name:"Symbiosis Institute of Business Management, Nagpur",faculty:"MANAGEMENT",city:"Nagpur"},{institute_code:"206",institute_short_name:"SCMHRD",institute_name:"Symbiosis Centre for Management and Human Resource Development",faculty:"MANAGEMENT",city:"Pune"},{institute_code:"301",institute_short_name:"SIT",institute_name:"Symbiosis Institute of Technology",faculty:"ENGINEERING",city:"Pune"},{institute_code:"302",institute_short_name:"SITB",institute_name:"Symbiosis Institute of Technology, Bengaluru",faculty:"ENGINEERING",city:"Bengaluru"},{institute_code:"303",institute_short_name:"SITH",institute_name:"Symbiosis Institute of Technology, Hyderabad",faculty:"ENGINEERING",city:"Hyderabad"},{institute_code:"304",institute_short_name:"SITN",institute_name:"Symbiosis Institute of Technology, Nagpur",faculty:"ENGINEERING",city:"Nagpur"},{institute_code:"401",institute_short_name:"SICSR",institute_name:"Symbiosis Institute of Computer Studies and Research",faculty:"COMPUTER_SCIENCE",city:"Pune"},{institute_code:"402",institute_short_name:"SICSR-N",institute_name:"Symbiosis Institute of Computer Studies and Research, Nagpur",faculty:"COMPUTER_SCIENCE",city:"Nagpur"},{institute_code:"501",institute_short_name:"SIMC",institute_name:"Symbiosis Institute of Media and Communication",faculty:"MEDIA",city:"Pune"},{institute_code:"502",institute_short_name:"SIMC-B",institute_name:"Symbiosis Institute of Media and Communication, Bengaluru",faculty:"MEDIA",city:"Bengaluru"},{institute_code:"601",institute_short_name:"SID",institute_name:"Symbiosis Institute of Design",faculty:"DESIGN",city:"Pune"},{institute_code:"701",institute_short_name:"SIU-CON",institute_name:"Symbiosis College of Nursing",faculty:"HEALTH_SCIENCES",city:"Pune"},{institute_code:"702",institute_short_name:"SIHSRC",institute_name:"Symbiosis Institute of Health Sciences Research Centre",faculty:"HEALTH_SCIENCES",city:"Pune"},{institute_code:"801",institute_short_name:"SITM",institute_name:"Symbiosis Institute of Telecom Management",faculty:"TELECOM",city:"Pune"},{institute_code:"901",institute_short_name:"SIOM",institute_name:"Symbiosis Institute of Operations Management",faculty:"OPERATIONS",city:"Nashik"},{institute_code:"1001",institute_short_name:"SIS",institute_name:"Symbiosis Statistical Institute",faculty:"STATISTICS",city:"Pune"},{institute_code:"1101",institute_short_name:"SSE",institute_name:"Symbiosis School of Economics",faculty:"ECONOMICS",city:"Pune"},{institute_code:"1201",institute_short_name:"SSBF",institute_name:"Symbiosis School of Banking and Finance",faculty:"FINANCE",city:"Pune"},{institute_code:"1301",institute_short_name:"SSLA",institute_name:"Symbiosis School of Liberal Arts",faculty:"LIBERAL_ARTS",city:"Pune"},{institute_code:"1401",institute_short_name:"SICA",institute_name:"Symbiosis Institute of Culinary Arts",faculty:"CULINARY",city:"Pune"},{institute_code:"1501",institute_short_name:"SICG",institute_name:"Symbiosis Institute of Geoinformatics",faculty:"GEOINFORMATICS",city:"Pune"},{institute_code:"1601",institute_short_name:"SSIIS",institute_name:"Symbiosis School of International Studies",faculty:"INTERNATIONAL_STUDIES",city:"Pune"},{institute_code:"1701",institute_short_name:"SSSS",institute_name:"Symbiosis School of Sports Sciences",faculty:"SPORTS",city:"Pune"},{institute_code:"1801",institute_short_name:"SSOP",institute_name:"Symbiosis School of Photography",faculty:"PHOTOGRAPHY",city:"Pune"},{institute_code:"1901",institute_short_name:"SSPAD",institute_name:"Symbiosis School of Planning Architecture and Design",faculty:"ARCHITECTURE",city:"Pune"},{institute_code:"2001",institute_short_name:"SSBS",institute_name:"Symbiosis School of Biological Sciences",faculty:"BIOLOGICAL_SCIENCES",city:"Pune"},{institute_code:"2101",institute_short_name:"SSPA",institute_name:"Symbiosis School of Performing Arts",faculty:"PERFORMING_ARTS",city:"Pune"},{institute_code:"2201",institute_short_name:"SSPU",institute_name:"Symbiosis Skills and Professional University",faculty:"SKILLS",city:"Pune"},{institute_code:"2301",institute_short_name:"SCDL",institute_name:"Symbiosis Centre for Distance Learning",faculty:"DISTANCE_LEARNING",city:"Pune"},{institute_code:"2401",institute_short_name:"SCIT",institute_name:"Symbiosis Centre for Information Technology",faculty:"INFORMATION_TECHNOLOGY",city:"Pune"},{institute_code:"2501",institute_short_name:"SCRI",institute_name:"Symbiosis Centre for Research and Innovation",faculty:"RESEARCH",city:"Pune"},{institute_code:"2601",institute_short_name:"SIS-P",institute_name:"Symbiosis International School, Pune",faculty:"SCHOOL",city:"Pune"},{institute_code:"2701",institute_short_name:"SCCE",institute_name:"Symbiosis Centre for Corporate Education",faculty:"CORPORATE_EDUCATION",city:"Pune"},{institute_code:"2801",institute_short_name:"SIFIL",institute_name:"Symbiosis Institute of Foreign and Indian Languages",faculty:"LANGUAGES",city:"Pune"},{institute_code:"2901",institute_short_name:"SCMS-P",institute_name:"Symbiosis Centre for Management Studies, Pune",faculty:"MANAGEMENT",city:"Pune"},{institute_code:"2902",institute_short_name:"SCMS-N",institute_name:"Symbiosis Centre for Management Studies, Noida",faculty:"MANAGEMENT",city:"Noida"}],p=[{metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",criteria_number:"1.1"},{metric_number:"1.1.1a",metric_description:"Global developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.1b",metric_description:"National developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.1c",metric_description:"Regional developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.1d",metric_description:"Local developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.3",metric_description:"Percentage of students undertaking project work/field work/internships",criteria_number:"1.1"},{metric_number:"1.3.1",metric_description:"Institution integrates crosscutting issues relevant to Professional Ethics, Gender, Human Values, Environment and Sustainability into the Curriculum",criteria_number:"1.3"},{metric_number:"1.3.1a",metric_description:"Number of courses that include focus on gender issues during the last five years",criteria_number:"1.3"},{metric_number:"1.3.2a",metric_description:"Number of value-added courses for imparting transferable and life skills offered during last five years",criteria_number:"1.3"},{metric_number:"1.3.2b",metric_description:"Number of students enrolled in the courses under 1.3.2 above",criteria_number:"1.3"},{metric_number:"1.3.4",metric_description:"Percentage of students undertaking field projects/internships/student projects",criteria_number:"1.3"},{metric_number:"1.4.1",metric_description:"Institution obtains feedback on the syllabus and its transaction at the institution from the following stakeholders",criteria_number:"1.4"},{metric_number:"1.4.2",metric_description:"Feedback process of the Institution may be classified as follows",criteria_number:"1.4"},{metric_number:"2.2.1",metric_description:"Student – Full time Teacher Ratio",criteria_number:"2.2"},{metric_number:"2.3.1",metric_description:"Student centric methods, such as experiential learning, participative learning and problem solving methodologies are used for enhancing learning experiences",criteria_number:"2.3"},{metric_number:"2.3.2",metric_description:"Teachers use ICT enabled tools for effective teaching-learning process",criteria_number:"2.3"},{metric_number:"2.3.3",metric_description:"Ratio of students to mentor for academic and other related issues",criteria_number:"2.3"},{metric_number:"2.4.4",metric_description:"Percentage of full time teachers against sanctioned posts during the last five years",criteria_number:"2.4"},{metric_number:"2.6.1",metric_description:"Programme and course outcomes for all Programmes offered by the institution are stated and displayed on website and communicated to teachers and students",criteria_number:"2.6"},{metric_number:"2.6.2",metric_description:"Attainment of Programme outcomes and course outcomes are evaluated by the institution",criteria_number:"2.6"},{metric_number:"3.1.5",metric_description:"Institution has the following facilities for research",criteria_number:"3.1"},{metric_number:"3.3.2",metric_description:"Number of workshops/seminars conducted on Research Methodology, Intellectual Property Rights (IPR) and entrepreneurship during the last five years",criteria_number:"3.3"},{metric_number:"3.3.3",metric_description:"Number of awards/recognitions received for extension activities from government/government recognised bodies during the last five years",criteria_number:"3.3"},{metric_number:"3.4.7",metric_description:"Number of registered patents (published/not published) and patents (Granted) during the last five years",criteria_number:"3.4"},{metric_number:"3.6.1",metric_description:"Extension activities are carried out in the neighborhood community, sensitizing students to social issues, for their holistic development, and impact thereof during the last five years",criteria_number:"3.6"},{metric_number:"3.6.2",metric_description:"Number of awards and recognitions received for extension activities from government/government recognised bodies during the last five years",criteria_number:"3.6"},{metric_number:"3.6.3",metric_description:"Number of extension and outreach programs conducted by the institution through NSS/NCC/Red cross/YRC etc., during the last five years",criteria_number:"3.6"},{metric_number:"3.6.4",metric_description:"Average percentage of students participating in extension activities at 3.6.3 above during last five years",criteria_number:"3.6"},{metric_number:"3.7.1",metric_description:"Number of Collaborative activities for research, Faculty exchange, Student exchange/internship during the last five years",criteria_number:"3.7"},{metric_number:"4.1.1",metric_description:"The Institution has adequate infrastructure and physical facilities for teaching-learning. viz., classrooms, laboratories, computing equipment etc.",criteria_number:"4.1"},{metric_number:"4.3.3",metric_description:"Bandwidth of internet connection in the Institution",criteria_number:"4.3"},{metric_number:"4.3.5",metric_description:"Institution has the following Facilities for e-content development",criteria_number:"4.3"},{metric_number:"5.1.2",metric_description:"Number of capability enhancement and development schemes such as Soft skill development, Remedial coaching, Language lab, Bridge courses, Yoga, Meditation, Personal Counselling and Mentoring etc., for students",criteria_number:"5.1"},{metric_number:"5.1.3a",metric_description:"Number of students benefitted by guidance for competitive examinations and career counselling offered by the Institution during the last five years",criteria_number:"5.1"},{metric_number:"5.1.3b",metric_description:"Number of students who have passed in the qualifying examination conducted by national/state/government or other recognized bodies during the last five years",criteria_number:"5.1"},{metric_number:"5.2.1",metric_description:"Average percentage of placement of outgoing students during the last five years",criteria_number:"5.2"},{metric_number:"5.2.2",metric_description:"Average percentage of students progressing to higher education during the last five years",criteria_number:"5.2"},{metric_number:"5.2.3",metric_description:"Average percentage of students qualifying in state/national/international level examinations during the last five years",criteria_number:"5.2"},{metric_number:"5.3.1",metric_description:"Number of awards/medals won by students for outstanding performance in sports/cultural activities at inter-university/state/national/international level during the last five years",criteria_number:"5.3"},{metric_number:"5.3.2",metric_description:"Institution facilitates students representation and engagement in various administrative, co-curricular and extracurricular activities",criteria_number:"5.3"},{metric_number:"5.3.3",metric_description:"Average number of sports and cultural programs in which students of the Institution participated during last five years",criteria_number:"5.3"},{metric_number:"5.4.1",metric_description:"There is a registered Alumni Association that contributes significantly to the development of the institution through financial and/or other support services",criteria_number:"5.4"},{metric_number:"6.3.3",metric_description:"Average number of professional development/administrative training programs organized by the institution for teaching and non-teaching staff during the last five years",criteria_number:"6.3"},{metric_number:"6.3.4",metric_description:"Average percentage of teachers undergoing online/face-to-face Faculty Development Programmes (FDP) during the last five years",criteria_number:"6.3"},{metric_number:"6.5.2",metric_description:"The institution reviews its teaching learning process, structures & methodologies of operations and learning outcomes at periodic intervals through IQAC set up as per norms and recorded the incremental improvement in various activities",criteria_number:"6.5"},{metric_number:"7.1.1",metric_description:"Measures initiated by the Institution for the promotion of gender equity during the last five years",criteria_number:"7.1"},{metric_number:"7.1.8",metric_description:"Average percentage of expenditure for infrastructure augmentation excluding salary during the last five years",criteria_number:"7.1"},{metric_number:"7.1.9",metric_description:"Sensitization of students and employees of the Institution to the constitutional obligations: values, rights, duties and responsibilities of citizens",criteria_number:"7.1"},{metric_number:"7.1.10",metric_description:"The Institution has a prescribed code of conduct for students, teachers, administrators and other staff and conducts periodic programmes in this regard",criteria_number:"7.1"},{metric_number:"7.1.11",metric_description:"Institution celebrates/organizes national and international commemorative days, events and festivals",criteria_number:"7.1"},{metric_number:"7.2.1",metric_description:"Describe two best practices successfully implemented by the Institution as per NAAC format provided in the Manual",criteria_number:"7.2"},{metric_number:"7.3.1",metric_description:"Portray the performance of the Institution in one area distinctive to its priority and thrust within a maximum of 500 words",criteria_number:"7.3"}],h=e=>p.find(t=>t.metric_number===e),b=[{id:"rule_001",metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",column_name:"Programme_Name",is_required:!0,data_type:"text",validation_type:"required_field",validation_params:"",error_message:"Programme Name is required"},{id:"rule_002",metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",column_name:"Academic_Year",is_required:!0,data_type:"text",validation_type:"year_range",validation_params:"2019-2024",error_message:"Academic Year must be between 2019-2024"},{id:"rule_003",metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",column_name:"Institute_Code",is_required:!0,data_type:"text",validation_type:"institute_code_choice",validation_params:"",reference_table:"institutions",error_message:"Invalid Institute Code"},{id:"rule_004",metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",column_name:"Institute_Name",is_required:!0,data_type:"text",validation_type:"institute_name_choice",validation_params:"",reference_table:"institutions",error_message:"Invalid Institute Name"},{id:"rule_005",metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",column_name:"Faculty",is_required:!0,data_type:"text",validation_type:"faculty_choice",validation_params:"LAW,MANAGEMENT,ENGINEERING,COMPUTER_SCIENCE,MEDIA,DESIGN,HEALTH_SCIENCES,TELECOM,OPERATIONS,STATISTICS,ECONOMICS,FINANCE,LIBERAL_ARTS,CULINARY,GEOINFORMATICS,INTERNATIONAL_STUDIES,SPORTS,PHOTOGRAPHY,ARCHITECTURE,BIOLOGICAL_SCIENCES,PERFORMING_ARTS,SKILLS,DISTANCE_LEARNING,INFORMATION_TECHNOLOGY,RESEARCH,SCHOOL,CORPORATE_EDUCATION,LANGUAGES",error_message:"Invalid Faculty selection"},{id:"rule_006",metric_number:"1.1.1a",metric_description:"Global developmental needs which the curricula address",column_name:"Global_Need_Description",is_required:!0,data_type:"text",validation_type:"word_limit",validation_params:"500",error_message:"Global Need Description must not exceed 500 words"},{id:"rule_007",metric_number:"1.1.1a",metric_description:"Global developmental needs which the curricula address",column_name:"Evidence_Document",is_required:!0,data_type:"file",validation_type:"pdf_file",validation_params:"",error_message:"Evidence document must be a PDF file"},{id:"rule_008",metric_number:"1.1.1b",metric_description:"National developmental needs which the curricula address",column_name:"National_Need_Description",is_required:!0,data_type:"text",validation_type:"word_limit",validation_params:"500",error_message:"National Need Description must not exceed 500 words"},{id:"rule_009",metric_number:"1.1.3",metric_description:"Percentage of students undertaking project work/field work/internships",column_name:"Total_Students",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:10000",error_message:"Total Students must be a number between 1 and 10000"},{id:"rule_010",metric_number:"1.1.3",metric_description:"Percentage of students undertaking project work/field work/internships",column_name:"Students_With_Projects",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:0,max:10000",error_message:"Students with Projects must be a number between 0 and 10000"},{id:"rule_011",metric_number:"1.3.1",metric_description:"Institution integrates crosscutting issues relevant to Professional Ethics, Gender, Human Values, Environment and Sustainability",column_name:"Professional_Ethics",is_required:!0,data_type:"text",validation_type:"yes_no_choice",validation_params:"Yes,No",error_message:"Professional Ethics must be Yes or No"},{id:"rule_012",metric_number:"1.3.1",metric_description:"Institution integrates crosscutting issues relevant to Professional Ethics, Gender, Human Values, Environment and Sustainability",column_name:"Gender_Issues",is_required:!0,data_type:"text",validation_type:"yes_no_choice",validation_params:"Yes,No",error_message:"Gender Issues must be Yes or No"},{id:"rule_013",metric_number:"2.2.1",metric_description:"Student – Full time Teacher Ratio",column_name:"Total_Students_Enrolled",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:50000",error_message:"Total Students Enrolled must be between 1 and 50000"},{id:"rule_014",metric_number:"2.2.1",metric_description:"Student – Full time Teacher Ratio",column_name:"Total_Fulltime_Teachers",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:5000",error_message:"Total Fulltime Teachers must be between 1 and 5000"},{id:"rule_015",metric_number:"3.3.2",metric_description:"Number of workshops/seminars conducted on Research Methodology, Intellectual Property Rights (IPR) and entrepreneurship",column_name:"Workshop_Title",is_required:!0,data_type:"text",validation_type:"text_length",validation_params:"min:10,max:200",error_message:"Workshop Title must be between 10 and 200 characters"},{id:"rule_016",metric_number:"3.3.2",metric_description:"Number of workshops/seminars conducted on Research Methodology, Intellectual Property Rights (IPR) and entrepreneurship",column_name:"Date_Conducted",is_required:!0,data_type:"date",validation_type:"date",validation_params:"format:DD/MM/YYYY,range:01/01/2019-31/12/2024",error_message:"Date must be in DD/MM/YYYY format between 01/01/2019 and 31/12/2024"},{id:"rule_017",metric_number:"4.3.3",metric_description:"Bandwidth of internet connection in the Institution",column_name:"Bandwidth_Mbps",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:10000",error_message:"Bandwidth must be between 1 and 10000 Mbps"},{id:"rule_018",metric_number:"4.3.3",metric_description:"Bandwidth of internet connection in the Institution",column_name:"Service_Provider",is_required:!0,data_type:"text",validation_type:"required_field",validation_params:"",error_message:"Service Provider is required"},{id:"rule_019",metric_number:"5.2.1",metric_description:"Average percentage of placement of outgoing students during the last five years",column_name:"Students_Eligible_For_Placement",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:0,max:10000",error_message:"Students Eligible for Placement must be between 0 and 10000"},{id:"rule_020",metric_number:"5.2.1",metric_description:"Average percentage of placement of outgoing students during the last five years",column_name:"Students_Placed",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:0,max:10000",error_message:"Students Placed must be between 0 and 10000"},{id:"rule_021",metric_number:"6.3.4",metric_description:"Average percentage of teachers undergoing online/face-to-face Faculty Development Programmes (FDP)",column_name:"FDP_Type",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Online,Face-to-Face,Hybrid",error_message:"FDP Type must be Online, Face-to-Face, or Hybrid"},{id:"rule_022",metric_number:"6.3.4",metric_description:"Average percentage of teachers undergoing online/face-to-face Faculty Development Programmes (FDP)",column_name:"Duration_Hours",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:720",error_message:"Duration must be between 1 and 720 hours"},{id:"rule_023",metric_number:"7.1.8",metric_description:"Average percentage of expenditure for infrastructure augmentation excluding salary",column_name:"Infrastructure_Expenditure",is_required:!0,data_type:"currency",validation_type:"currency",validation_params:"min:0,max:100000000",error_message:"Infrastructure Expenditure must be a valid currency amount"},{id:"rule_024",metric_number:"7.1.8",metric_description:"Average percentage of expenditure for infrastructure augmentation excluding salary",column_name:"Total_Expenditure",is_required:!0,data_type:"currency",validation_type:"currency",validation_params:"min:1,max:1000000000",error_message:"Total Expenditure must be a valid currency amount"},{id:"rule_025",metric_number:"1.3.1a",metric_description:"Number of courses that include focus on gender issues during the last five years",column_name:"Course_Name",is_required:!0,data_type:"text",validation_type:"required_field",validation_params:"",error_message:"Course Name is required"},{id:"rule_026",metric_number:"1.3.1a",metric_description:"Number of courses that include focus on gender issues during the last five years",column_name:"Gender_Focus_Description",is_required:!0,data_type:"text",validation_type:"word_limit",validation_params:"200",error_message:"Gender Focus Description must not exceed 200 words"},{id:"rule_027",metric_number:"1.3.2a",metric_description:"Number of value-added courses for imparting transferable and life skills",column_name:"Course_Type",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Transferable Skills,Life Skills,Professional Skills,Communication Skills,Leadership Skills",error_message:"Course Type must be one of the specified skill categories"},{id:"rule_028",metric_number:"1.3.2a",metric_description:"Number of value-added courses for imparting transferable and life skills",column_name:"Duration_Hours",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:500",error_message:"Duration must be between 1 and 500 hours"},{id:"rule_029",metric_number:"1.3.2b",metric_description:"Number of students enrolled in the courses under 1.3.2 above",column_name:"Students_Enrolled",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:1000",error_message:"Students Enrolled must be between 1 and 1000"},{id:"rule_030",metric_number:"1.3.4",metric_description:"Percentage of students undertaking field projects/internships/student projects",column_name:"Project_Type",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Field Project,Internship,Student Project,Research Project",error_message:"Project Type must be Field Project, Internship, Student Project, or Research Project"},{id:"rule_031",metric_number:"1.4.1",metric_description:"Institution obtains feedback on the syllabus and its transaction from stakeholders",column_name:"Stakeholder_Type",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Students,Teachers,Employers,Alumni,Parents",error_message:"Stakeholder Type must be Students, Teachers, Employers, Alumni, or Parents"},{id:"rule_032",metric_number:"1.4.1",metric_description:"Institution obtains feedback on the syllabus and its transaction from stakeholders",column_name:"Feedback_Method",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Online Survey,Physical Form,Interview,Focus Group,Email",error_message:"Feedback Method must be one of the specified methods"},{id:"rule_033",metric_number:"1.4.2",metric_description:"Feedback process of the Institution classification",column_name:"Process_Type",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"A. Feedback collected, analysed and action taken and feedback available on website,B. Feedback collected, analysed and action has been taken,C. Feedback collected and analysed,D. Feedback collected,E. Feedback not obtained",error_message:"Process Type must be one of the specified feedback process classifications"},{id:"rule_034",metric_number:"2.3.1",metric_description:"Student centric methods for enhancing learning experiences",column_name:"Teaching_Method",is_required:!0,data_type:"text",validation_type:"contains_text",validation_params:"experiential,participative,problem solving,case study,simulation,role play",error_message:"Teaching Method must contain student-centric learning approaches"},{id:"rule_035",metric_number:"2.3.2",metric_description:"Teachers use ICT enabled tools for effective teaching-learning process",column_name:"ICT_Tool",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"LMS,Video Conferencing,Interactive Whiteboard,Educational Software,Online Assessment,Virtual Lab,Mobile App,E-books",error_message:"ICT Tool must be one of the specified technology tools"},{id:"rule_036",metric_number:"2.3.3",metric_description:"Ratio of students to mentor for academic and other related issues",column_name:"Total_Mentors",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:500",error_message:"Total Mentors must be between 1 and 500"},{id:"rule_037",metric_number:"2.4.4",metric_description:"Percentage of full time teachers against sanctioned posts during the last five years",column_name:"Sanctioned_Posts",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:1000",error_message:"Sanctioned Posts must be between 1 and 1000"},{id:"rule_038",metric_number:"2.6.1",metric_description:"Programme and course outcomes displayed on website and communicated",column_name:"Website_URL",is_required:!0,data_type:"url",validation_type:"url_pattern",validation_params:"",error_message:"Website URL must be a valid URL"},{id:"rule_039",metric_number:"2.6.1",metric_description:"Programme and course outcomes displayed on website and communicated",column_name:"Communication_Method",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Website,Student Handbook,Orientation Program,Course Syllabus,Notice Board",error_message:"Communication Method must be one of the specified methods"},{id:"rule_040",metric_number:"2.6.2",metric_description:"Attainment of Programme outcomes and course outcomes are evaluated",column_name:"Evaluation_Method",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Direct Assessment,Indirect Assessment,Course Exit Survey,Alumni Survey,Employer Survey",error_message:"Evaluation Method must be one of the specified assessment methods"},{id:"rule_041",metric_number:"ALL",metric_description:"Common validation for all metrics",column_name:"Remarks",is_required:!1,data_type:"text",validation_type:"word_limit",validation_params:"1000",error_message:"Remarks must not exceed 1000 words"},{id:"rule_042",metric_number:"ALL",metric_description:"Common validation for all metrics",column_name:"Supporting_Document_URL",is_required:!1,data_type:"url",validation_type:"url_pattern",validation_params:"",error_message:"Supporting Document URL must be a valid URL"}],f=e=>b.filter(t=>t.metric_number===e||"ALL"===t.metric_number);class g{async validateCSV(e,t,i){let r=new Date,a=f(t),n=[],s=this.validateHeaders(e.headers,a);n.push(...s);for(let t=0;t<e.rows.length;t++){let i=e.rows[t],r=this.validateRow(i,e.headers,a,t+2);n.push(...r)}let o=this.generateValidationSummary(n,e.rows.length);return{isValid:0===n.filter(e=>"error"===e.severity).length,errorCount:n.filter(e=>"error"===e.severity).length,warningCount:n.filter(e=>"warning"===e.severity).length,errors:n,summary:o,processedRows:e.rows.length,timestamp:r}}validateHeaders(e,t){let i=[];for(let r of t.filter(e=>e.is_required).map(e=>e.column_name))e.includes(r)||i.push({row:1,column:r,value:"",error:"Required column '".concat(r,"' is missing from CSV headers"),rule:"required_field",severity:"error"});return i}validateRow(e,t,i,r){let a=[];for(let n of i){let i=t.indexOf(n.column_name);if(-1===i)continue;let s=e[i]||"",o=this.validators[n.validation_type];if(o){let t=o(s,n.validation_params,r,[e]);t.isValid||a.push({row:r,column:n.column_name,value:s,error:t.error||n.error_message,rule:n.validation_type,severity:n.is_required?"error":"warning"})}}return a}generateValidationSummary(e,t){let i=new Set(e.filter(e=>"error"===e.severity).map(e=>e.row)).size,r=new Set(e.filter(e=>"warning"===e.severity).map(e=>e.row)).size,a={};return e.forEach(e=>{let t="".concat(e.rule,": ").concat(e.error);a[t]=(a[t]||0)+1}),{totalRows:t,validRows:t-i,errorRows:i,warningRows:r,criticalErrors:e.filter(e=>"error"===e.severity).slice(0,10),commonErrors:a}}validateYearRange(e,t){if(!e.trim())return{isValid:!0};let[i,r]=t.split("-").map(e=>parseInt(e)),a=parseInt(e);return isNaN(a)?{isValid:!1,error:"Invalid year format"}:a<i||a>r?{isValid:!1,error:"Year must be between ".concat(i," and ").concat(r)}:{isValid:!0}}validateExactMatch(e,t){if(!e.trim())return{isValid:!0};let i=t.split(",").map(e=>e.trim());return i.includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Value must be one of: ".concat(i.join(", "))}}validateContainsText(e,t){if(!e.trim())return{isValid:!0};let i=t.split(",").map(e=>e.trim().toLowerCase()),r=e.toLowerCase();return i.some(e=>r.includes(e))?{isValid:!0}:{isValid:!1,error:"Text must contain one of: ".concat(i.join(", "))}}validateFacultyChoice(e,t){if(!e.trim())return{isValid:!0};let i=t.split(",").map(e=>e.trim());return i.includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Invalid faculty. Must be one of: ".concat(i.join(", "))}}validateInstituteCodeChoice(e){return e.trim()?_.map(e=>e.institute_code).includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Invalid institute code"}:{isValid:!0}}validateInstituteShortNameChoice(e){return e.trim()?_.map(e=>e.institute_short_name).includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Invalid institute short name"}:{isValid:!0}}validateInstituteNameChoice(e){return e.trim()?_.map(e=>e.institute_name).includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Invalid institute name"}:{isValid:!0}}validateFixedDigits(e,t){if(!e.trim())return{isValid:!0};let i=parseInt(t);return/^\d+$/.test(e)&&e.length===i?{isValid:!0}:{isValid:!1,error:"Must be exactly ".concat(i," digits")}}validateRequiredField(e){return e&&e.trim()?{isValid:!0}:{isValid:!1,error:"This field is required"}}validateWordLimit(e,t){if(!e.trim())return{isValid:!0};let i=parseInt(t),r=e.trim().split(/\s+/).length;return r>i?{isValid:!1,error:"Exceeds word limit of ".concat(i," words (current: ").concat(r,")")}:{isValid:!0}}validatePdfFile(e){return e.trim()?e.toLowerCase().endsWith(".pdf")?{isValid:!0}:{isValid:!1,error:"File must be a PDF"}:{isValid:!0}}validateYesNoChoice(e){return e.trim()?["Yes","No","YES","NO","yes","no"].includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Value must be Yes or No"}:{isValid:!0}}validateBinaryChoice(e,t){if(!e.trim())return{isValid:!0};let[i,r]=t.split(",").map(e=>e.trim());return[i,r].includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Value must be ".concat(i," or ").concat(r)}}validateTextLength(e,t){if(!e.trim())return{isValid:!0};let[i,r]=t.split(","),a=parseInt(i.split(":")[1]),n=parseInt(r.split(":")[1]);return e.length<a||e.length>n?{isValid:!1,error:"Text length must be between ".concat(a," and ").concat(n," characters")}:{isValid:!0}}validateYear(e){if(!e.trim())return{isValid:!0};let t=parseInt(e),i=new Date().getFullYear();return isNaN(t)||t<1900||t>i+10?{isValid:!1,error:"Invalid year. Must be between 1900 and ".concat(i+10)}:{isValid:!0}}validateNumeric(e,t){if(!e.trim())return{isValid:!0};let i=parseFloat(e);if(isNaN(i))return{isValid:!1,error:"Value must be a number"};if(t)for(let e of t.split(",")){let[t,r]=e.split(":"),a=parseFloat(r);if("min"===t&&i<a)return{isValid:!1,error:"Value must be at least ".concat(a)};if("max"===t&&i>a)return{isValid:!1,error:"Value must not exceed ".concat(a)}}return{isValid:!0}}validateCurrency(e,t){if(!e.trim())return{isValid:!0};let i=e.replace(/[₹$,\s]/g,"");return isNaN(parseFloat(i))?{isValid:!1,error:"Invalid currency format"}:this.validateNumeric(i,t)}validateUrlPattern(e){if(!e.trim())return{isValid:!0};try{return new URL(e),{isValid:!0}}catch(e){return{isValid:!1,error:"Invalid URL format"}}}validateImageFile(e){return e.trim()?[".jpg",".jpeg",".png",".gif",".bmp",".webp"].some(t=>e.toLowerCase().endsWith(t))?{isValid:!0}:{isValid:!1,error:"File must be an image (jpg, jpeg, png, gif, bmp, webp)"}:{isValid:!0}}validateDate(e,t){if(!e.trim())return{isValid:!0};let i=t.split(","),r="DD/MM/YYYY",a="";for(let e of i){let[t,i]=e.split(":");"format"===t&&(r=i),"range"===t&&(a=i)}let n=e.match(/^(\d{2})\/(\d{2})\/(\d{4})$/);if(!n)return{isValid:!1,error:"Date must be in ".concat(r," format")};let[,s,o,c]=n,l=new Date(parseInt(c),parseInt(o)-1,parseInt(s));if(l.getDate()!==parseInt(s)||l.getMonth()!==parseInt(o)-1||l.getFullYear()!==parseInt(c))return{isValid:!1,error:"Invalid date"};if(a){let[e,t]=a.split("-"),[i,r,n]=e.split("/"),[s,o,c]=t.split("/"),d=new Date(parseInt(n),parseInt(r)-1,parseInt(i)),m=new Date(parseInt(c),parseInt(o)-1,parseInt(s));if(l<d||l>m)return{isValid:!1,error:"Date must be between ".concat(e," and ").concat(t)}}return{isValid:!0}}validateReferenceLookup(e,t){return!e.trim(),{isValid:!0}}constructor(){this.validators={year_range:this.validateYearRange.bind(this),exact_match:this.validateExactMatch.bind(this),contains_text:this.validateContainsText.bind(this),faculty_choice:this.validateFacultyChoice.bind(this),institute_code_choice:this.validateInstituteCodeChoice.bind(this),institute_shortname_choice:this.validateInstituteShortNameChoice.bind(this),institute_name_choice:this.validateInstituteNameChoice.bind(this),fixed_digits:this.validateFixedDigits.bind(this),required_field:this.validateRequiredField.bind(this),word_limit:this.validateWordLimit.bind(this),pdf_file:this.validatePdfFile.bind(this),yes_no_choice:this.validateYesNoChoice.bind(this),binary_choice:this.validateBinaryChoice.bind(this),text_length:this.validateTextLength.bind(this),year:this.validateYear.bind(this),numeric:this.validateNumeric.bind(this),currency:this.validateCurrency.bind(this),url_pattern:this.validateUrlPattern.bind(this),image_file:this.validateImageFile.bind(this),date:this.validateDate.bind(this),reference_lookup:this.validateReferenceLookup.bind(this)}}}var y=i(408),v=i.n(y);class x{static async parseCSVFile(e){return new Promise((t,i)=>{v().parse(e,{header:!1,skipEmptyLines:!0,complete:r=>{try{if(r.errors.length>0)return void i(Error("CSV parsing error: ".concat(r.errors[0].message)));let a=r.data;if(0===a.length)return void i(Error("CSV file is empty"));let n=a[0],s=a.slice(1),o={headers:n,rows:s,fileName:e.name,fileSize:e.size,rowCount:s.length};t(o)}catch(e){i(e)}},error:e=>{i(Error("Failed to parse CSV: ".concat(e.message)))}})})}static validateCSVFile(e){return e.name.toLowerCase().endsWith(".csv")?e.size>0xa00000?{isValid:!1,error:"File size must not exceed 10MB"}:0===e.size?{isValid:!1,error:"File cannot be empty"}:{isValid:!0}:{isValid:!1,error:"File must be a CSV file"}}static convertToCSV(e,t){let i=[e,...t];return v().unparse(i)}static downloadCSV(e,t){let i=new Blob([e],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a");if(void 0!==r.download){let e=URL.createObjectURL(i);r.setAttribute("href",e),r.setAttribute("download",t),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r)}}static cleanCSVData(e){let t=e.headers.map(e=>e.trim().replace(/\s+/g,"_")),i=e.rows.map(e=>e.map(e=>e?e.toString().trim():""));return{...e,headers:t,rows:i}}static getCSVStatistics(e){let t={totalRows:e.rows.length,totalColumns:e.headers.length,emptyRows:0,emptyColumns:0,columnStats:{}};return t.emptyRows=e.rows.filter(e=>e.every(e=>!e||!e.trim())).length,e.headers.forEach((i,r)=>{let a=e.rows.map(e=>e[r]||""),n=a.filter(e=>""!==e.trim()),s=new Set(n);t.columnStats[i]={filled:n.length,empty:a.length-n.length,unique:s.size}}),t}static validateCSVStructure(e,t){let i=[],r=t.filter(t=>!e.headers.includes(t));r.length>0&&i.push("Missing required columns: ".concat(r.join(", ")));let a=e.headers.filter(e=>!t.includes(e));a.length>0&&i.push("Unexpected columns found: ".concat(a.join(", ")));let n=e.rows.filter(t=>t.length!==e.headers.length);return n.length>0&&i.push("".concat(n.length," rows have inconsistent column count")),{isValid:0===i.length,errors:i}}static sampleCSVData(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;return{...e,rows:e.rows.slice(0,t),rowCount:Math.min(e.rowCount,t)}}static csvToTableData(e){let t=e.rows.map(t=>{let i={};return e.headers.forEach((e,r)=>{i[e]=t[r]||""}),i});return{headers:e.headers,rows:t}}static filterCSVData(e,t){let i=e.rows.filter(i=>Object.entries(t).every(t=>{let[r,a]=t,n=e.headers.indexOf(r);return -1===n||(i[n]||"").toLowerCase().includes(a.toLowerCase())}));return{...e,rows:i,rowCount:i.length}}static sortCSVData(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"asc",r=e.headers.indexOf(t);if(-1===r)return e;let a=[...e.rows].sort((e,t)=>{let a=e[r]||"",n=t[r]||"",s=parseFloat(a),o=parseFloat(n);return isNaN(s)||isNaN(o)?"asc"===i?a.localeCompare(n):n.localeCompare(a):"asc"===i?s-o:o-s});return{...e,rows:a}}static getUniqueColumnValues(e,t){let i=e.headers.indexOf(t);return -1===i?[]:[...new Set(e.rows.map(e=>e[i]||""))].filter(e=>""!==e.trim()).sort()}static detectColumnTypes(e){let t={};return e.headers.forEach((i,r)=>{let a=e.rows.map(e=>e[r]||"").filter(e=>""!==e.trim());if(0===a.length){t[i]="empty";return}if(a.every(e=>!isNaN(parseFloat(e)))){t[i]="numeric";return}if(a.every(e=>!isNaN(new Date(e).getTime()))){t[i]="date";return}if(a.every(e=>{try{return new URL(e),!0}catch(e){return!1}})){t[i]="url";return}t[i]="text"}),t}}class S{static generateTemplate(e){let t=h(e),i=f(e);if(!t)throw Error("Metric ".concat(e," not found"));let r=this.generateTemplateColumns(i),a=this.generateSampleData(r),n=this.generateInstructions(e,i);return{metricNumber:e,metricDescription:t.metric_description,columns:r,instructions:n,sampleData:a}}static generateTemplateColumns(e){let t=new Map;return e.forEach(e=>{t.has(e.column_name)||t.set(e.column_name,{name:e.column_name,description:this.getColumnDescription(e),dataType:e.data_type,required:e.is_required,validationHint:this.getValidationHint(e),sampleValue:this.generateSampleValue(e)})}),Array.from(t.values()).sort((e,t)=>e.required&&!t.required?-1:!e.required&&t.required?1:e.name.localeCompare(t.name))}static getColumnDescription(e){return({Programme_Name:"Name of the academic programme",Academic_Year:"Academic year in YYYY-YYYY format",Institute_Code:"Official institute code from SIU",Institute_Name:"Full name of the institute",Faculty:"Faculty/Department name",Total_Students:"Total number of students",Students_With_Projects:"Number of students with projects/internships",Global_Need_Description:"Description of global developmental needs addressed",National_Need_Description:"Description of national developmental needs addressed",Evidence_Document:"Supporting document file path",Professional_Ethics:"Whether professional ethics is integrated (Yes/No)",Gender_Issues:"Whether gender issues are addressed (Yes/No)",Workshop_Title:"Title of the workshop/seminar",Date_Conducted:"Date when workshop was conducted",Bandwidth_Mbps:"Internet bandwidth in Mbps",Service_Provider:"Internet service provider name",Students_Eligible_For_Placement:"Number of students eligible for placement",Students_Placed:"Number of students successfully placed",FDP_Type:"Type of Faculty Development Programme",Duration_Hours:"Duration of programme in hours",Infrastructure_Expenditure:"Amount spent on infrastructure",Total_Expenditure:"Total expenditure amount",Remarks:"Additional remarks or comments",Supporting_Document_URL:"URL to supporting documents"})[e.column_name]||"Data for ".concat(e.column_name.replace(/_/g," "))}static getValidationHint(e){switch(e.validation_type){case"year_range":return"Year between ".concat(e.validation_params);case"exact_match":return"Must be one of: ".concat(e.validation_params);case"numeric":return"Numeric value ".concat(e.validation_params?"(".concat(e.validation_params,")"):"");case"word_limit":return"Maximum ".concat(e.validation_params," words");case"text_length":return"Text length ".concat(e.validation_params);case"yes_no_choice":return"Enter Yes or No";case"date":return"Date in DD/MM/YYYY format";case"currency":return"Currency amount (e.g., 100000)";case"url_pattern":return"Valid URL starting with http:// or https://";case"pdf_file":return"PDF file path or name";case"image_file":return"Image file path (jpg, png, etc.)";case"required_field":return"This field is mandatory";case"institute_code_choice":return"Valid SIU institute code";case"institute_name_choice":return"Valid SIU institute name";case"faculty_choice":return"Valid faculty name";default:return"Follow the specified format"}}static generateSampleValue(e){switch(e.validation_type){case"year_range":let[t]=e.validation_params.split("-");return t||"2023";case"exact_match":var i;return(null==(i=e.validation_params.split(",")[0])?void 0:i.trim())||"Option1";case"numeric":return"100";case"word_limit":return"Sample text content";case"yes_no_choice":return"Yes";case"date":return"01/01/2023";case"currency":return"100000";case"url_pattern":return"https://example.com/document.pdf";case"pdf_file":return"document.pdf";case"image_file":return"image.jpg";case"institute_code_choice":return"101";case"institute_name_choice":return"Symbiosis Law School, Pune";case"faculty_choice":return"LAW";default:return this.getDefaultSampleValue(e.column_name)}}static getDefaultSampleValue(e){return({Programme_Name:"Bachelor of Laws (LL.B.)",Academic_Year:"2023-2024",Institute_Code:"101",Institute_Name:"Symbiosis Law School, Pune",Faculty:"LAW",Total_Students:"150",Students_With_Projects:"120",Workshop_Title:"Research Methodology Workshop",Service_Provider:"Airtel Business",Bandwidth_Mbps:"100",Remarks:"Additional information if any"})[e]||"Sample Value"}static generateSampleData(e){let t=[];for(let i=0;i<3;i++){let r=e.map(e=>"Academic_Year"===e.name?"".concat(2021+i,"-").concat(2022+i):e.name.includes("Total")||e.name.includes("Students")?(100+10*i).toString():e.sampleValue);t.push(r)}return t}static generateInstructions(e,t){let i=["Template for Metric ".concat(e),"Please follow these guidelines when filling the template:","","General Instructions:","• Fill all required fields (marked with *)","• Use the exact format specified for each field","• Do not modify column headers","• Ensure data consistency across all rows","• Save the file in CSV format before uploading","","Field-specific Instructions:"];return[...new Set(t.map(e=>e.validation_type))].forEach(e=>{switch(e){case"year_range":i.push("• Academic years should be in YYYY-YYYY format (e.g., 2023-2024)");break;case"institute_code_choice":i.push("• Use only valid SIU institute codes (101, 102, 201, etc.)");break;case"date":i.push("• Dates should be in DD/MM/YYYY format (e.g., 15/03/2023)");break;case"numeric":i.push("• Numeric fields should contain only numbers");break;case"currency":i.push("• Currency amounts should be in numbers only (without symbols)");break;case"yes_no_choice":i.push('• Yes/No fields should contain exactly "Yes" or "No"');break;case"word_limit":i.push("• Respect word limits for text fields");break;case"pdf_file":i.push("• File references should include .pdf extension")}}),i.push(""),i.push("For support, contact: <EMAIL>"),i}static downloadTemplate(e){let t=this.generateTemplate(e),i=t.columns.map(e=>e.name),r=x.convertToCSV(i,t.sampleData),a="AQAR_Template_".concat(e,"_").concat(new Date().toISOString().split("T")[0],".csv");x.downloadCSV(r,a)}static generateTemplateWithInstructions(e){let t=this.generateTemplate(e),i="";t.instructions.forEach(e=>{i+="# ".concat(e,"\n")}),i+="\n",i+="# Column Descriptions:\n",t.columns.forEach(e=>{let t=e.required?" (Required)":" (Optional)";i+="# ".concat(e.name).concat(t,": ").concat(e.description,"\n"),i+="#   Format: ".concat(e.validationHint,"\n"),i+="#   Example: ".concat(e.sampleValue,"\n")}),i+="\n";let r=t.columns.map(e=>e.name),a=x.convertToCSV(r,t.sampleData);return i+=a}static getTemplateSummary(e){let t=this.generateTemplate(e),i=t.columns.filter(e=>e.required).length,r=[...new Set(t.columns.map(e=>e.validationHint.split(" ")[0]))];return{metricNumber:t.metricNumber,metricDescription:t.metricDescription,totalColumns:t.columns.length,requiredColumns:i,optionalColumns:t.columns.length-i,validationTypes:r}}}let N=()=>{let[e,t]=(0,a.useState)({instituteCode:"",metricNumber:"",csvFile:null}),[i,h]=(0,a.useState)(null),[b,f]=(0,a.useState)(!1),[y,v]=(0,a.useState)(null),[N,w]=(0,a.useState)(""),I=(0,a.useCallback)(e=>{var i;let r=null==(i=e.target.files)?void 0:i[0];if(!r)return;let a=x.validateCSVFile(r);if(!a.isValid)return void w(a.error||"Invalid file");t(e=>({...e,csvFile:r})),w("")},[]),C=(0,a.useCallback)(async()=>{if(!e.csvFile||!e.instituteCode||!e.metricNumber)return void w("Please fill all required fields and upload a CSV file");f(!0),w("");try{let t=await x.parseCSVFile(e.csvFile);v(t);let i=new g,r=await i.validateCSV(t,e.metricNumber,e.instituteCode);h(r)}catch(e){w(e instanceof Error?e.message:"Validation failed")}finally{f(!1)}},[e]),E=(0,a.useCallback)(()=>{if(!e.metricNumber)return void w("Please select a metric first");try{S.downloadTemplate(e.metricNumber)}catch(e){w(e instanceof Error?e.message:"Failed to download template")}},[e.metricNumber]),j=(0,a.useCallback)(()=>{if(!(null==i?void 0:i.isValid))return void w("Cannot submit file with validation errors");alert("File submitted to QMB successfully!")},[i]),T=_.find(t=>t.institute_code===e.instituteCode),P=p.find(t=>t.metric_number===e.metricNumber);return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-red-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b-4 border-red-600",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"NAAC AQAR CSV Validation Tool"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 mt-2",children:"Symbiosis International University - Quality Assurance"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-red-600 rounded-full flex items-center justify-center",children:(0,r.jsx)(n.A,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,r.jsx)(s.A,{className:"w-6 h-6 text-white"})})]})]})})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-6",children:"Validation Form"}),N&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"w-5 h-5 text-red-500 mr-2"}),(0,r.jsx)("span",{className:"text-red-700",children:N})]})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,r.jsx)(s.A,{className:"w-4 h-4 inline mr-1"}),"Select Your Institution *"]}),(0,r.jsxs)("select",{value:e.instituteCode,onChange:e=>t(t=>({...t,instituteCode:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"",children:"Choose Institution..."}),_.map(e=>(0,r.jsxs)("option",{value:e.institute_code,children:[e.institute_code," - ",e.institute_short_name," - ",e.institute_name]},e.institute_code))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,r.jsx)(c.A,{className:"w-4 h-4 inline mr-1"}),"Select Metric *"]}),(0,r.jsxs)("select",{value:e.metricNumber,onChange:e=>t(t=>({...t,metricNumber:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"",children:"Choose Metric..."}),p.map(e=>(0,r.jsxs)("option",{value:e.metric_number,children:[e.metric_number," - ",e.metric_description]},e.metric_number))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,r.jsx)(l.A,{className:"w-4 h-4 inline mr-1"}),"Upload CSV File *"]}),(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors",children:[(0,r.jsx)("input",{type:"file",accept:".csv",onChange:I,className:"hidden",id:"csv-upload"}),(0,r.jsxs)("label",{htmlFor:"csv-upload",className:"cursor-pointer",children:[(0,r.jsx)(l.A,{className:"w-8 h-8 text-gray-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.csvFile?e.csvFile.name:"Click to upload CSV file"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Maximum file size: 10MB"})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsx)("button",{onClick:C,disabled:!e.csvFile||!e.instituteCode||!e.metricNumber||b,className:"flex-1 min-w-[200px] bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:b?"Validating...":"Validate CSV"}),(0,r.jsx)("button",{onClick:j,disabled:!(null==i?void 0:i.isValid),className:"flex-1 min-w-[200px] bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:"Submit to QMB"}),(0,r.jsxs)("button",{onClick:E,disabled:!e.metricNumber,className:"flex-1 min-w-[200px] bg-orange-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-orange-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 inline mr-2"}),"Download Template"]})]})]})]})}),(0,r.jsxs)("div",{className:"space-y-6",children:[T&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Selected Institution"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Code:"})," ",T.institute_code]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Name:"})," ",T.institute_name]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Faculty:"})," ",T.faculty]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"City:"})," ",T.city]})]})]}),P&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Selected Metric"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Number:"})," ",P.metric_number]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Criteria:"})," ",P.criteria_number]}),(0,r.jsx)("p",{className:"text-gray-600",children:P.metric_description})]})]}),y&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"CSV File Info"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"File:"})," ",y.fileName]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Size:"})," ",(y.fileSize/1024).toFixed(1)," KB"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Rows:"})," ",y.rowCount]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Columns:"})," ",y.headers.length]})]})]})]})]}),i&&(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:"Validation Results"}),(0,r.jsx)("div",{className:"flex items-center",children:i.isValid?(0,r.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,r.jsx)(m.A,{className:"w-6 h-6 mr-2"}),(0,r.jsx)("span",{className:"font-medium",children:"Validation Passed"})]}):(0,r.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,r.jsx)(o.A,{className:"w-6 h-6 mr-2"}),(0,r.jsx)("span",{className:"font-medium",children:"Validation Failed"})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:i.processedRows}),(0,r.jsx)("div",{className:"text-sm text-blue-800",children:"Total Rows"})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:i.summary.validRows}),(0,r.jsx)("div",{className:"text-sm text-green-800",children:"Valid Rows"})]}),(0,r.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:i.errorCount}),(0,r.jsx)("div",{className:"text-sm text-red-800",children:"Errors"})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:i.warningCount}),(0,r.jsx)("div",{className:"text-sm text-yellow-800",children:"Warnings"})]})]}),i.errors.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Error Details"}),(0,r.jsx)("div",{className:"max-h-96 overflow-y-auto",children:(0,r.jsxs)("div",{className:"space-y-2",children:[i.errors.slice(0,50).map((e,t)=>(0,r.jsx)("div",{className:"p-3 rounded-lg border-l-4 ".concat("error"===e.severity?"bg-red-50 border-red-400":"bg-yellow-50 border-yellow-400"),children:(0,r.jsxs)("div",{className:"flex items-start",children:["error"===e.severity?(0,r.jsx)(o.A,{className:"w-5 h-5 text-red-500 mr-2 mt-0.5"}):(0,r.jsx)(u.A,{className:"w-5 h-5 text-yellow-500 mr-2 mt-0.5"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("p",{className:"font-medium text-gray-900",children:["Row ",e.row,', Column "',e.column,'"']}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.error}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:['Value: "',e.value,'" | Rule: ',e.rule]})]})]})},t)),i.errors.length>50&&(0,r.jsxs)("div",{className:"text-center py-4 text-gray-500",children:["... and ",i.errors.length-50," more errors"]})]})})]})]})})]})]})};function w(){return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,r.jsx)(N,{})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[96,76,358],()=>t(1813)),_N_E=e.O()}]);