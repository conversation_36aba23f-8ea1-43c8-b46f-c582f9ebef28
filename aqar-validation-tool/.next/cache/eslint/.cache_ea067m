[{"/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/api/stats/route.ts": "1", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/api/submit/route.ts": "2", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/api/template/route.ts": "3", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/api/validate/route.ts": "4", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/layout.tsx": "5", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/page.tsx": "6", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/components/AQARValidationTool.tsx": "7", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/data/institutions.ts": "8", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/data/metrics.ts": "9", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/data/validationRules.ts": "10", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/lib/fileManager.ts": "11", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/lib/templateGenerator.ts": "12", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/lib/validationEngine.ts": "13", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/types/index.ts": "14", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/utils/csvProcessor.ts": "15", "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/utils/testHelpers.ts": "16"}, {"size": 12002, "mtime": 1750321817099, "results": "17", "hashOfConfig": "18"}, {"size": 5733, "mtime": 1750321755107, "results": "19", "hashOfConfig": "18"}, {"size": 4825, "mtime": 1750321715950, "results": "20", "hashOfConfig": "18"}, {"size": 4864, "mtime": 1750321687106, "results": "21", "hashOfConfig": "18"}, {"size": 689, "mtime": 1750320908026, "results": "22", "hashOfConfig": "18"}, {"size": 223, "mtime": 1750321425751, "results": "23", "hashOfConfig": "18"}, {"size": 16672, "mtime": 1750321491240, "results": "24", "hashOfConfig": "18"}, {"size": 8820, "mtime": 1750321047892, "results": "25", "hashOfConfig": "18"}, {"size": 11251, "mtime": 1750322165763, "results": "26", "hashOfConfig": "18"}, {"size": 19824, "mtime": 1750321546833, "results": "27", "hashOfConfig": "18"}, {"size": 13380, "mtime": 1750321644697, "results": "28", "hashOfConfig": "18"}, {"size": 12124, "mtime": 1750321367719, "results": "29", "hashOfConfig": "18"}, {"size": 15760, "mtime": 1750321264576, "results": "30", "hashOfConfig": "18"}, {"size": 4394, "mtime": 1750320997306, "results": "31", "hashOfConfig": "18"}, {"size": 9729, "mtime": 1750321307867, "results": "32", "hashOfConfig": "18"}, {"size": 12494, "mtime": 1750321891473, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "oyqndp", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/api/stats/route.ts", ["82", "83", "84"], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/api/submit/route.ts", ["85"], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/api/template/route.ts", [], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/api/validate/route.ts", [], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/layout.tsx", [], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/page.tsx", [], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/components/AQARValidationTool.tsx", ["86", "87", "88", "89"], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/data/institutions.ts", [], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/data/metrics.ts", [], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/data/validationRules.ts", [], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/lib/fileManager.ts", ["90", "91", "92"], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/lib/templateGenerator.ts", [], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/lib/validationEngine.ts", ["93", "94"], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/types/index.ts", ["95"], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/utils/csvProcessor.ts", [], [], "/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/utils/testHelpers.ts", ["96", "97", "98", "99", "100"], [], {"ruleId": "101", "severity": 2, "message": "102", "line": 162, "column": 14, "nodeType": "103", "messageId": "104", "endLine": 162, "endColumn": 17, "suggestions": "105"}, {"ruleId": "101", "severity": 2, "message": "102", "line": 238, "column": 14, "nodeType": "103", "messageId": "104", "endLine": 238, "endColumn": 17, "suggestions": "106"}, {"ruleId": "101", "severity": 2, "message": "102", "line": 338, "column": 48, "nodeType": "103", "messageId": "104", "endLine": 338, "endColumn": 51, "suggestions": "107"}, {"ruleId": "101", "severity": 2, "message": "102", "line": 162, "column": 50, "nodeType": "103", "messageId": "104", "endLine": 162, "endColumn": 53, "suggestions": "108"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 353, "column": 57, "nodeType": "111", "messageId": "112", "suggestions": "113"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 353, "column": 72, "nodeType": "111", "messageId": "112", "suggestions": "114"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 357, "column": 40, "nodeType": "111", "messageId": "112", "suggestions": "115"}, {"ruleId": "109", "severity": 2, "message": "110", "line": 357, "column": 54, "nodeType": "111", "messageId": "112", "suggestions": "116"}, {"ruleId": "117", "severity": 2, "message": "118", "line": 84, "column": 43, "nodeType": null, "messageId": "119", "endLine": 84, "endColumn": 47}, {"ruleId": "117", "severity": 2, "message": "120", "line": 84, "column": 55, "nodeType": null, "messageId": "119", "endLine": 84, "endColumn": 63}, {"ruleId": "101", "severity": 2, "message": "102", "line": 287, "column": 50, "nodeType": "103", "messageId": "104", "endLine": 287, "endColumn": 53, "suggestions": "121"}, {"ruleId": "117", "severity": 2, "message": "122", "line": 43, "column": 68, "nodeType": null, "messageId": "119", "endLine": 43, "endColumn": 81}, {"ruleId": "117", "severity": 2, "message": "123", "line": 445, "column": 50, "nodeType": null, "messageId": "119", "endLine": 445, "endColumn": 56}, {"ruleId": "101", "severity": 2, "message": "102", "line": 135, "column": 34, "nodeType": "103", "messageId": "104", "endLine": 135, "endColumn": 37, "suggestions": "124"}, {"ruleId": "117", "severity": 2, "message": "125", "line": 26, "column": 7, "nodeType": null, "messageId": "119", "endLine": 26, "endColumn": 21}, {"ruleId": "117", "severity": 2, "message": "126", "line": 119, "column": 84, "nodeType": null, "messageId": "119", "endLine": 119, "endColumn": 89}, {"ruleId": "101", "severity": 2, "message": "102", "line": 356, "column": 46, "nodeType": "103", "messageId": "104", "endLine": 356, "endColumn": 49, "suggestions": "127"}, {"ruleId": "101", "severity": 2, "message": "102", "line": 359, "column": 52, "nodeType": "103", "messageId": "104", "endLine": 359, "endColumn": 55, "suggestions": "128"}, {"ruleId": "101", "severity": 2, "message": "102", "line": 379, "column": 72, "nodeType": "103", "messageId": "104", "endLine": 379, "endColumn": 75, "suggestions": "129"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["130", "131"], ["132", "133"], ["134", "135"], ["136", "137"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["138", "139", "140", "141"], ["142", "143", "144", "145"], ["146", "147", "148", "149"], ["150", "151", "152", "153"], "@typescript-eslint/no-unused-vars", "'file' is defined but never used.", "unusedVar", "'filePath' is defined but never used.", ["154", "155"], "'instituteCode' is defined but never used.", "'params' is defined but never used.", ["156", "157"], "'includeHeaders' is assigned a value but never used.", "'index' is defined but never used.", ["158", "159"], ["160", "161"], ["162", "163"], {"messageId": "164", "fix": "165", "desc": "166"}, {"messageId": "167", "fix": "168", "desc": "169"}, {"messageId": "164", "fix": "170", "desc": "166"}, {"messageId": "167", "fix": "171", "desc": "169"}, {"messageId": "164", "fix": "172", "desc": "166"}, {"messageId": "167", "fix": "173", "desc": "169"}, {"messageId": "164", "fix": "174", "desc": "166"}, {"messageId": "167", "fix": "175", "desc": "169"}, {"messageId": "176", "data": "177", "fix": "178", "desc": "179"}, {"messageId": "176", "data": "180", "fix": "181", "desc": "182"}, {"messageId": "176", "data": "183", "fix": "184", "desc": "185"}, {"messageId": "176", "data": "186", "fix": "187", "desc": "188"}, {"messageId": "176", "data": "189", "fix": "190", "desc": "179"}, {"messageId": "176", "data": "191", "fix": "192", "desc": "182"}, {"messageId": "176", "data": "193", "fix": "194", "desc": "185"}, {"messageId": "176", "data": "195", "fix": "196", "desc": "188"}, {"messageId": "176", "data": "197", "fix": "198", "desc": "179"}, {"messageId": "176", "data": "199", "fix": "200", "desc": "182"}, {"messageId": "176", "data": "201", "fix": "202", "desc": "185"}, {"messageId": "176", "data": "203", "fix": "204", "desc": "188"}, {"messageId": "176", "data": "205", "fix": "206", "desc": "179"}, {"messageId": "176", "data": "207", "fix": "208", "desc": "182"}, {"messageId": "176", "data": "209", "fix": "210", "desc": "185"}, {"messageId": "176", "data": "211", "fix": "212", "desc": "188"}, {"messageId": "164", "fix": "213", "desc": "166"}, {"messageId": "167", "fix": "214", "desc": "169"}, {"messageId": "164", "fix": "215", "desc": "166"}, {"messageId": "167", "fix": "216", "desc": "169"}, {"messageId": "164", "fix": "217", "desc": "166"}, {"messageId": "167", "fix": "218", "desc": "169"}, {"messageId": "164", "fix": "219", "desc": "166"}, {"messageId": "167", "fix": "220", "desc": "169"}, {"messageId": "164", "fix": "221", "desc": "166"}, {"messageId": "167", "fix": "222", "desc": "169"}, "suggestUnknown", {"range": "223", "text": "224"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "225", "text": "226"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "227", "text": "224"}, {"range": "228", "text": "226"}, {"range": "229", "text": "224"}, {"range": "230", "text": "226"}, {"range": "231", "text": "224"}, {"range": "232", "text": "226"}, "replaceWithAlt", {"alt": "233"}, {"range": "234", "text": "235"}, "Replace with `&quot;`.", {"alt": "236"}, {"range": "237", "text": "238"}, "Replace with `&ldquo;`.", {"alt": "239"}, {"range": "240", "text": "241"}, "Replace with `&#34;`.", {"alt": "242"}, {"range": "243", "text": "244"}, "Replace with `&rdquo;`.", {"alt": "233"}, {"range": "245", "text": "246"}, {"alt": "236"}, {"range": "247", "text": "248"}, {"alt": "239"}, {"range": "249", "text": "250"}, {"alt": "242"}, {"range": "251", "text": "252"}, {"alt": "233"}, {"range": "253", "text": "254"}, {"alt": "236"}, {"range": "255", "text": "256"}, {"alt": "239"}, {"range": "257", "text": "258"}, {"alt": "242"}, {"range": "259", "text": "260"}, {"alt": "233"}, {"range": "261", "text": "262"}, {"alt": "236"}, {"range": "263", "text": "264"}, {"alt": "239"}, {"range": "265", "text": "266"}, {"alt": "242"}, {"range": "267", "text": "268"}, {"range": "269", "text": "224"}, {"range": "270", "text": "226"}, {"range": "271", "text": "224"}, {"range": "272", "text": "226"}, {"range": "273", "text": "224"}, {"range": "274", "text": "226"}, {"range": "275", "text": "224"}, {"range": "276", "text": "226"}, {"range": "277", "text": "224"}, {"range": "278", "text": "226"}, [5245, 5248], "unknown", [5245, 5248], "never", [7879, 7882], [7879, 7882], [11321, 11324], [11321, 11324], [4539, 4542], [4539, 4542], "&quot;", [15768, 15778], ", Column &quot;", "&ldquo;", [15768, 15778], ", Column &ldquo;", "&#34;", [15768, 15778], ", Column &#34;", "&rdquo;", [15768, 15778], ", Column &rdquo;", [15792, 15824], "&quot;\n                              ", [15792, 15824], "&ldquo;\n                              ", [15792, 15824], "&#34;\n                              ", [15792, 15824], "&rdquo;\n                              ", [15986, 16027], "\n                                Value: &quot;", [15986, 16027], "\n                                Value: &ldquo;", [15986, 16027], "\n                                Value: &#34;", [15986, 16027], "\n                                Value: &rdquo;", [16040, 16050], "&quot; | Rule: ", [16040, 16050], "&ldquo; | Rule: ", [16040, 16050], "&#34; | Rule: ", [16040, 16050], "&rdquo; | Rule: ", [8599, 8602], [8599, 8602], [2805, 2808], [2805, 2808], [11058, 11061], [11058, 11061], [11154, 11157], [11154, 11157], [11788, 11791], [11788, 11791]]