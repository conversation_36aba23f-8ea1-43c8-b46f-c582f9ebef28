"use strict";exports.id=928,exports.ids=[928],exports.modules={3928:(e,t,i)=>{var r,n;void 0===(n="function"==typeof(r=function e(){var t="undefined"!=typeof self?self:void 0!==t?t:{},r=!t.document&&!!t.postMessage,n=t.IS_PAPA_WORKER||!1,s={},a=0,o={};if(o.parse=function(i,r){var n,h=(r=r||{}).dynamicTyping||!1;if(C(h)&&(r.dynamicTypingFunction=h,h={}),r.dynamicTyping=h,r.transform=!!C(r.transform)&&r.transform,r.worker&&o.WORKERS_SUPPORTED){var u=function(){if(!o.WORKERS_SUPPORTED)return!1;var i,r,n=(i=t.URL||t.webkitURL||null,r=e.toString(),o.BLOB_URL||(o.BLOB_URL=i.createObjectURL(new Blob(["var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; ","(",r,")();"],{type:"text/javascript"})))),h=new t.Worker(n);return h.onmessage=y,h.id=a++,s[h.id]=h,h}();u.userStep=r.step,u.userChunk=r.chunk,u.userComplete=r.complete,u.userError=r.error,r.step=C(r.step),r.chunk=C(r.chunk),r.complete=C(r.complete),r.error=C(r.error),delete r.worker,u.postMessage({input:i,config:r,workerId:u.id});return}var _=null;return i===o.NODE_STREAM_INPUT&&"undefined"==typeof PAPA_BROWSER_CONTEXT?(_=new p(r)).getStream():("string"==typeof i?(i=65279===(n=i).charCodeAt(0)?n.slice(1):n,_=r.download?new f(r):new l(r)):!0===i.readable&&C(i.read)&&C(i.on)?_=new c(r):(t.File&&i instanceof File||i instanceof Object)&&(_=new d(r)),_.stream(i))},o.unparse=function(e,t){var i=!1,r=!0,n=",",s="\r\n",a='"',h=a+a,u=!1,f=null,d=!1;if("object"==typeof t){if("string"!=typeof t.delimiter||o.BAD_DELIMITERS.filter(function(e){return -1!==t.delimiter.indexOf(e)}).length||(n=t.delimiter),("boolean"==typeof t.quotes||"function"==typeof t.quotes||Array.isArray(t.quotes))&&(i=t.quotes),("boolean"==typeof t.skipEmptyLines||"string"==typeof t.skipEmptyLines)&&(u=t.skipEmptyLines),"string"==typeof t.newline&&(s=t.newline),"string"==typeof t.quoteChar&&(a=t.quoteChar),"boolean"==typeof t.header&&(r=t.header),Array.isArray(t.columns)){if(0===t.columns.length)throw Error("Option columns is empty");f=t.columns}void 0!==t.escapeChar&&(h=t.escapeChar+a),t.escapeFormulae instanceof RegExp?d=t.escapeFormulae:"boolean"==typeof t.escapeFormulae&&t.escapeFormulae&&(d=/^[=+\-@\t\r].*$/)}var l=RegExp(g(a),"g");if("string"==typeof e&&(e=JSON.parse(e)),Array.isArray(e)){if(!e.length||Array.isArray(e[0]))return c(null,e,u);else if("object"==typeof e[0])return c(f||Object.keys(e[0]),e,u)}else if("object"==typeof e)return"string"==typeof e.data&&(e.data=JSON.parse(e.data)),Array.isArray(e.data)&&(e.fields||(e.fields=e.meta&&e.meta.fields||f),e.fields||(e.fields=Array.isArray(e.data[0])?e.fields:"object"==typeof e.data[0]?Object.keys(e.data[0]):[]),Array.isArray(e.data[0])||"object"==typeof e.data[0]||(e.data=[e.data])),c(e.fields||[],e.data||[],u);throw Error("Unable to serialize unrecognized input");function c(e,t,i){var a="";"string"==typeof e&&(e=JSON.parse(e)),"string"==typeof t&&(t=JSON.parse(t));var o=Array.isArray(e)&&e.length>0,h=!Array.isArray(t[0]);if(o&&r){for(var u=0;u<e.length;u++)u>0&&(a+=n),a+=p(e[u],u);t.length>0&&(a+=s)}for(var f=0;f<t.length;f++){var d=o?e.length:t[f].length,l=!1,c=o?0===Object.keys(t[f]).length:0===t[f].length;if(i&&!o&&(l="greedy"===i?""===t[f].join("").trim():1===t[f].length&&0===t[f][0].length),"greedy"===i&&o){for(var _=[],g=0;g<d;g++){var m=h?e[g]:g;_.push(t[f][m])}l=""===_.join("").trim()}if(!l){for(var y=0;y<d;y++){y>0&&!c&&(a+=n);var v=o&&h?e[y]:y;a+=p(t[f][v],y)}f<t.length-1&&(!i||d>0&&!c)&&(a+=s)}}return a}function p(e,t){if(null==e)return"";if(e.constructor===Date)return JSON.stringify(e).slice(1,25);var r=!1;d&&"string"==typeof e&&d.test(e)&&(e="'"+e,r=!0);var s=e.toString().replace(l,h);return(r=r||!0===i||"function"==typeof i&&i(e,t)||Array.isArray(i)&&i[t]||function(e,t){for(var i=0;i<t.length;i++)if(e.indexOf(t[i])>-1)return!0;return!1}(s,o.BAD_DELIMITERS)||s.indexOf(n)>-1||" "===s.charAt(0)||" "===s.charAt(s.length-1))?a+s+a:s}},o.RECORD_SEP="\x1e",o.UNIT_SEP="\x1f",o.BYTE_ORDER_MARK="\uFEFF",o.BAD_DELIMITERS=["\r","\n",'"',o.BYTE_ORDER_MARK],o.WORKERS_SUPPORTED=!r&&!!t.Worker,o.NODE_STREAM_INPUT=1,o.LocalChunkSize=0xa00000,o.RemoteChunkSize=5242880,o.DefaultDelimiter=",",o.Parser=m,o.ParserHandle=_,o.NetworkStreamer=f,o.FileStreamer=d,o.StringStreamer=l,o.ReadableStreamStreamer=c,"undefined"==typeof PAPA_BROWSER_CONTEXT&&(o.DuplexStreamStreamer=p),t.jQuery){var h=t.jQuery;h.fn.parse=function(e){var i=e.config||{},r=[];return this.each(function(e){if(!("INPUT"===h(this).prop("tagName").toUpperCase()&&"file"===h(this).attr("type").toLowerCase()&&t.FileReader)||!this.files||0===this.files.length)return!0;for(var n=0;n<this.files.length;n++)r.push({file:this.files[n],inputElem:this,instanceConfig:h.extend({},i)})}),n(),this;function n(){if(0===r.length){C(e.complete)&&e.complete();return}var t=r[0];if(C(e.before)){var i,n,a,u,f=e.before(t.file,t.inputElem);if("object"==typeof f)if("abort"===f.action){return void(i="AbortError",n=t.file,a=t.inputElem,u=f.reason,C(e.error)&&e.error({name:i},n,a,u))}else{if("skip"===f.action)return void s();"object"==typeof f.config&&(t.instanceConfig=h.extend(t.instanceConfig,f.config))}else if("skip"===f)return void s()}var d=t.instanceConfig.complete;t.instanceConfig.complete=function(e){C(d)&&d(e,t.file,t.inputElem),s()},o.parse(t.file,t.instanceConfig)}function s(){r.splice(0,1),n()}}}function u(e){this._handle=null,this._finished=!1,this._completed=!1,this._halted=!1,this._input=null,this._baseIndex=0,this._partialLine="",this._rowCount=0,this._start=0,this._nextChunk=null,this.isFirstChunk=!0,this._completeResults={data:[],errors:[],meta:{}},(function(e){var t=E(e);t.chunkSize=parseInt(t.chunkSize),e.step||e.chunk||(t.chunkSize=null),this._handle=new _(t),this._handle.streamer=this,this._config=t}).call(this,e),this.parseChunk=function(e,i){let r=parseInt(this._config.skipFirstNLines)||0;if(this.isFirstChunk&&r>0){let t=this._config.newline;if(!t){let i=this._config.quoteChar||'"';t=this._handle.guessLineEndings(e,i)}e=[...e.split(t).slice(r)].join(t)}if(this.isFirstChunk&&C(this._config.beforeFirstChunk)){var s=this._config.beforeFirstChunk(e);void 0!==s&&(e=s)}this.isFirstChunk=!1,this._halted=!1;var a=this._partialLine+e;this._partialLine="";var h=this._handle.parse(a,this._baseIndex,!this._finished);if(this._handle.paused()||this._handle.aborted()){this._halted=!0;return}var u=h.meta.cursor;this._finished||(this._partialLine=a.substring(u-this._baseIndex),this._baseIndex=u),h&&h.data&&(this._rowCount+=h.data.length);var f=this._finished||this._config.preview&&this._rowCount>=this._config.preview;if(n)t.postMessage({results:h,workerId:o.WORKER_ID,finished:f});else if(C(this._config.chunk)&&!i){if(this._config.chunk(h,this._handle),this._handle.paused()||this._handle.aborted()){this._halted=!0;return}h=void 0,this._completeResults=void 0}return this._config.step||this._config.chunk||(this._completeResults.data=this._completeResults.data.concat(h.data),this._completeResults.errors=this._completeResults.errors.concat(h.errors),this._completeResults.meta=h.meta),!this._completed&&f&&C(this._config.complete)&&(!h||!h.meta.aborted)&&(this._config.complete(this._completeResults,this._input),this._completed=!0),f||h&&h.meta.paused||this._nextChunk(),h},this._sendError=function(e){C(this._config.error)?this._config.error(e):n&&this._config.error&&t.postMessage({workerId:o.WORKER_ID,error:e,finished:!1})}}function f(e){var t;(e=e||{}).chunkSize||(e.chunkSize=o.RemoteChunkSize),u.call(this,e),r?this._nextChunk=function(){this._readChunk(),this._chunkLoaded()}:this._nextChunk=function(){this._readChunk()},this.stream=function(e){this._input=e,this._nextChunk()},this._readChunk=function(){if(this._finished)return void this._chunkLoaded();if(t=new XMLHttpRequest,this._config.withCredentials&&(t.withCredentials=this._config.withCredentials),r||(t.onload=b(this._chunkLoaded,this),t.onerror=b(this._chunkError,this)),t.open(this._config.downloadRequestBody?"POST":"GET",this._input,!r),this._config.downloadRequestHeaders){var e=this._config.downloadRequestHeaders;for(var i in e)t.setRequestHeader(i,e[i])}if(this._config.chunkSize){var n=this._start+this._config.chunkSize-1;t.setRequestHeader("Range","bytes="+this._start+"-"+n)}try{t.send(this._config.downloadRequestBody)}catch(e){this._chunkError(e.message)}r&&0===t.status&&this._chunkError()},this._chunkLoaded=function(){if(4===t.readyState){var e;if(t.status<200||t.status>=400)return void this._chunkError();this._start+=this._config.chunkSize?this._config.chunkSize:t.responseText.length,this._finished=!this._config.chunkSize||this._start>=(null===(e=t.getResponseHeader("Content-Range"))?-1:parseInt(e.substring(e.lastIndexOf("/")+1))),this.parseChunk(t.responseText)}},this._chunkError=function(e){var i=t.statusText||e;this._sendError(Error(i))}}function d(e){(e=e||{}).chunkSize||(e.chunkSize=o.LocalChunkSize),u.call(this,e);var t,i,r="undefined"!=typeof FileReader;this.stream=function(e){this._input=e,i=e.slice||e.webkitSlice||e.mozSlice,r?((t=new FileReader).onload=b(this._chunkLoaded,this),t.onerror=b(this._chunkError,this)):t=new FileReaderSync,this._nextChunk()},this._nextChunk=function(){this._finished||this._config.preview&&!(this._rowCount<this._config.preview)||this._readChunk()},this._readChunk=function(){var e=this._input;if(this._config.chunkSize){var n=Math.min(this._start+this._config.chunkSize,this._input.size);e=i.call(e,this._start,n)}var s=t.readAsText(e,this._config.encoding);r||this._chunkLoaded({target:{result:s}})},this._chunkLoaded=function(e){this._start+=this._config.chunkSize,this._finished=!this._config.chunkSize||this._start>=this._input.size,this.parseChunk(e.target.result)},this._chunkError=function(){this._sendError(t.error)}}function l(e){var t;e=e||{},u.call(this,e),this.stream=function(e){return t=e,this._nextChunk()},this._nextChunk=function(){if(!this._finished){var e,i=this._config.chunkSize;return i?(e=t.substring(0,i),t=t.substring(i)):(e=t,t=""),this._finished=!t,this.parseChunk(e)}}}function c(e){e=e||{},u.call(this,e);var t=[],i=!0,r=!1;this.pause=function(){u.prototype.pause.apply(this,arguments),this._input.pause()},this.resume=function(){u.prototype.resume.apply(this,arguments),this._input.resume()},this.stream=function(e){this._input=e,this._input.on("data",this._streamData),this._input.on("end",this._streamEnd),this._input.on("error",this._streamError)},this._checkIsFinished=function(){r&&1===t.length&&(this._finished=!0)},this._nextChunk=function(){this._checkIsFinished(),t.length?this.parseChunk(t.shift()):i=!0},this._streamData=b(function(e){try{t.push("string"==typeof e?e:e.toString(this._config.encoding)),i&&(i=!1,this._checkIsFinished(),this.parseChunk(t.shift()))}catch(e){this._streamError(e)}},this),this._streamError=b(function(e){this._streamCleanUp(),this._sendError(e)},this),this._streamEnd=b(function(){this._streamCleanUp(),r=!0,this._streamData("")},this),this._streamCleanUp=b(function(){this._input.removeListener("data",this._streamData),this._input.removeListener("end",this._streamEnd),this._input.removeListener("error",this._streamError)},this)}function p(e){var t=i(7910).Duplex,r=E(e),n=!0,s=!1,a=[],o=null;this._onCsvData=function(e){var t=e.data;o.push(t)||this._handle.paused()||this._handle.pause()},this._onCsvComplete=function(){o.push(null)},r.step=b(this._onCsvData,this),r.complete=b(this._onCsvComplete,this),u.call(this,r),this._nextChunk=function(){s&&1===a.length&&(this._finished=!0),a.length?a.shift()():n=!0},this._addToParseQueue=function(e,t){a.push(b(function(){if(this.parseChunk("string"==typeof e?e:e.toString(r.encoding)),C(t))return t()},this)),n&&(n=!1,this._nextChunk())},this._onRead=function(){this._handle.paused()&&this._handle.resume()},this._onWrite=function(e,t,i){this._addToParseQueue(e,i)},this._onWriteComplete=function(){s=!0,this._addToParseQueue("")},this.getStream=function(){return o},(o=new t({readableObjectMode:!0,decodeStrings:!1,read:b(this._onRead,this),write:b(this._onWrite,this)})).once("finish",b(this._onWriteComplete,this))}function _(e){var t,i,r,n=/^\s*-?(\d+\.?|\.\d+|\d+\.\d+)([eE][-+]?\d+)?\s*$/,s=/^((\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z)))$/,a=this,h=0,u=0,f=!1,d=!1,l=[],c={data:[],errors:[],meta:{}};if(C(e.step)){var p=e.step;e.step=function(t){if(c=t,v())y();else{if(y(),0===c.data.length)return;h+=t.data.length,e.preview&&h>e.preview?i.abort():(c.data=c.data[0],p(c,a))}}}function _(t){return"greedy"===e.skipEmptyLines?""===t.join("").trim():1===t.length&&0===t[0].length}function y(){return c&&r&&(k("Delimiter","UndetectableDelimiter","Unable to auto-detect delimiting character; defaulted to '"+o.DefaultDelimiter+"'"),r=!1),e.skipEmptyLines&&(c.data=c.data.filter(function(e){return!_(e)})),v()&&function(){if(c)if(Array.isArray(c.data[0])){for(var t=0;v()&&t<c.data.length;t++)c.data[t].forEach(i);c.data.splice(0,1)}else c.data.forEach(i);function i(t,i){C(e.transformHeader)&&(t=e.transformHeader(t,i)),l.push(t)}}(),function(){if(!c||!e.header&&!e.dynamicTyping&&!e.transform)return c;function t(t,i){var r,a=e.header?{}:[];for(r=0;r<t.length;r++){var o=r,h=t[r];e.header&&(o=r>=l.length?"__parsed_extra":l[r]),e.transform&&(h=e.transform(h,o)),h=function(t,i){if(e.dynamicTypingFunction&&void 0===e.dynamicTyping[t]&&(e.dynamicTyping[t]=e.dynamicTypingFunction(t)),!0===(e.dynamicTyping[t]||e.dynamicTyping))if("true"===i||"TRUE"===i)return!0;else if("false"===i||"FALSE"===i)return!1;else if(function(e){if(n.test(e)){var t=parseFloat(e);if(t>-0x20000000000000&&t<0x20000000000000)return!0}return!1}(i))return parseFloat(i);else if(s.test(i))return new Date(i);else return""===i?null:i;return i}(o,h),"__parsed_extra"===o?(a[o]=a[o]||[],a[o].push(h)):a[o]=h}return e.header&&(r>l.length?k("FieldMismatch","TooManyFields","Too many fields: expected "+l.length+" fields but parsed "+r,u+i):r<l.length&&k("FieldMismatch","TooFewFields","Too few fields: expected "+l.length+" fields but parsed "+r,u+i)),a}var i=1;return!c.data.length||Array.isArray(c.data[0])?(c.data=c.data.map(t),i=c.data.length):c.data=t(c.data,0),e.header&&c.meta&&(c.meta.fields=l),u+=i,c}()}function v(){return e.header&&0===l.length}function k(e,t,i,r){var n={type:e,code:t,message:i};void 0!==r&&(n.row=r),c.errors.push(n)}this.parse=function(n,s,a){var h=e.quoteChar||'"';if(e.newline||(e.newline=this.guessLineEndings(n,h)),r=!1,e.delimiter)C(e.delimiter)&&(e.delimiter=e.delimiter(n),c.meta.delimiter=e.delimiter);else{var u=function(t,i,r,n,s){var a,h,u,f;s=s||[",","	","|",";",o.RECORD_SEP,o.UNIT_SEP];for(var d=0;d<s.length;d++){var l=s[d],c=0,p=0,g=0;u=void 0;for(var y=new m({comments:n,delimiter:l,newline:i,preview:10}).parse(t),v=0;v<y.data.length;v++){if(r&&_(y.data[v])){g++;continue}var k=y.data[v].length;if(p+=k,void 0===u){u=k;continue}k>0&&(c+=Math.abs(k-u),u=k)}y.data.length>0&&(p/=y.data.length-g),(void 0===h||c<=h)&&(void 0===f||p>f)&&p>1.99&&(h=c,a=l,f=p)}return e.delimiter=a,{successful:!!a,bestDelimiter:a}}(n,e.newline,e.skipEmptyLines,e.comments,e.delimitersToGuess);u.successful?e.delimiter=u.bestDelimiter:(r=!0,e.delimiter=o.DefaultDelimiter),c.meta.delimiter=e.delimiter}var d=E(e);return e.preview&&e.header&&d.preview++,t=n,c=(i=new m(d)).parse(t,s,a),y(),f?{meta:{paused:!0}}:c||{meta:{paused:!1}}},this.paused=function(){return f},this.pause=function(){f=!0,i.abort(),t=C(e.chunk)?"":t.substring(i.getCharIndex())},this.resume=function(){a.streamer._halted?(f=!1,a.streamer.parseChunk(t,!0)):setTimeout(a.resume,3)},this.aborted=function(){return d},this.abort=function(){d=!0,i.abort(),c.meta.aborted=!0,C(e.complete)&&e.complete(c),t=""},this.guessLineEndings=function(e,t){e=e.substring(0,1048576);var i=RegExp(g(t)+"([^]*?)"+g(t),"gm"),r=(e=e.replace(i,"")).split("\r"),n=e.split("\n"),s=n.length>1&&n[0].length<r[0].length;if(1===r.length||s)return"\n";for(var a=0,o=0;o<r.length;o++)"\n"===r[o][0]&&a++;return a>=r.length/2?"\r\n":"\r"}}function g(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function m(e){var t,i=(e=e||{}).delimiter,r=e.newline,n=e.comments,s=e.step,a=e.preview,h=e.fastMode,u=null,f=!1,d=t=void 0===e.quoteChar||null===e.quoteChar?'"':e.quoteChar;if(void 0!==e.escapeChar&&(d=e.escapeChar),("string"!=typeof i||o.BAD_DELIMITERS.indexOf(i)>-1)&&(i=","),n===i)throw Error("Comment character same as delimiter");!0===n?n="#":("string"!=typeof n||o.BAD_DELIMITERS.indexOf(n)>-1)&&(n=!1),"\n"!==r&&"\r"!==r&&"\r\n"!==r&&(r="\n");var l=0,c=!1;this.parse=function(o,p,_){if("string"!=typeof o)throw Error("Input must be a string");var m=o.length,y=i.length,v=r.length,k=n.length,E=C(s);l=0;var b=[],R=[],w=[],S=0;if(!o)return U();if(h||!1!==h&&-1===o.indexOf(t)){for(var O=o.split(r),x=0;x<O.length;x++){if(w=O[x],l+=w.length,x!==O.length-1)l+=r.length;else if(_)break;if(!n||w.substring(0,k)!==n){if(E){if(b=[],P(w.split(i)),q(),c)return U()}else P(w.split(i));if(a&&x>=a)return b=b.slice(0,a),U(!0)}}return U()}for(var T=o.indexOf(i,l),A=o.indexOf(r,l),I=RegExp(g(d)+g(t),"g"),D=o.indexOf(t,l);;){if(o[l]===t){for(D=l,l++;;){if(-1===(D=o.indexOf(t,D+1)))return _||R.push({type:"Quotes",code:"MissingQuotes",message:"Quoted field unterminated",row:b.length,index:l}),z();if(D===m-1)return z(o.substring(l,D).replace(I,t));if(t===d&&o[D+1]===d){D++;continue}if(t===d||0===D||o[D-1]!==d){-1!==T&&T<D+1&&(T=o.indexOf(i,D+1)),-1!==A&&A<D+1&&(A=o.indexOf(r,D+1));var L=j(-1===A?T:Math.min(T,A));if(o.substr(D+1+L,y)===i){w.push(o.substring(l,D).replace(I,t)),l=D+1+L+y,o[D+1+L+y]!==t&&(D=o.indexOf(t,l)),T=o.indexOf(i,l),A=o.indexOf(r,l);break}var F=j(A);if(o.substring(D+1+F,D+1+F+v)===r){if(w.push(o.substring(l,D).replace(I,t)),M(D+1+F+v),T=o.indexOf(i,l),D=o.indexOf(t,l),E&&(q(),c))return U();if(a&&b.length>=a)return U(!0);break}R.push({type:"Quotes",code:"InvalidQuotes",message:"Trailing quote on quoted field is malformed",row:b.length,index:l}),D++;continue}}continue}if(n&&0===w.length&&o.substring(l,l+k)===n){if(-1===A)return U();l=A+v,A=o.indexOf(r,l),T=o.indexOf(i,l);continue}if(-1!==T&&(T<A||-1===A)){w.push(o.substring(l,T)),l=T+y,T=o.indexOf(i,l);continue}if(-1!==A){if(w.push(o.substring(l,A)),M(A+v),E&&(q(),c))return U();if(a&&b.length>=a)return U(!0);continue}break}return z();function P(e){b.push(e),S=l}function j(e){var t=0;if(-1!==e){var i=o.substring(D+1,e);i&&""===i.trim()&&(t=i.length)}return t}function z(e){return _||(void 0===e&&(e=o.substring(l)),w.push(e),l=m,P(w),E&&q()),U()}function M(e){l=e,P(w),w=[],A=o.indexOf(r,l)}function U(t){if(e.header&&!p&&b.length&&!f){let t=b[0],i=Object.create(null),r=new Set(t),n=!1;for(let s=0;s<t.length;s++){let a=t[s];if(C(e.transformHeader)&&(a=e.transformHeader(a,s)),i[a]){let e,o=i[a];do e=`${a}_${o}`,o++;while(r.has(e));r.add(e),t[s]=e,i[a]++,n=!0,null===u&&(u={}),u[e]=a}else i[a]=1,t[s]=a;r.add(a)}n&&console.warn("Duplicate headers found and renamed."),f=!0}return{data:b,errors:R,meta:{delimiter:i,linebreak:r,aborted:c,truncated:!!t,cursor:S+(p||0),renamedHeaders:u}}}function q(){s(U()),b=[],R=[]}},this.abort=function(){c=!0},this.getCharIndex=function(){return l}}function y(e){var t=e.data,i=s[t.workerId],r=!1;if(t.error)i.userError(t.error,t.file);else if(t.results&&t.results.data){var n={abort:function(){r=!0,v(t.workerId,{data:[],errors:[],meta:{aborted:!0}})},pause:k,resume:k};if(C(i.userStep)){for(var a=0;a<t.results.data.length&&(i.userStep({data:t.results.data[a],errors:t.results.errors,meta:t.results.meta},n),!r);a++);delete t.results}else C(i.userChunk)&&(i.userChunk(t.results,n,t.file),delete t.results)}t.finished&&!r&&v(t.workerId,t.results)}function v(e,t){var i=s[e];C(i.userComplete)&&i.userComplete(t),i.terminate(),delete s[e]}function k(){throw Error("Not implemented.")}function E(e){if("object"!=typeof e||null===e)return e;var t=Array.isArray(e)?[]:{};for(var i in e)t[i]=E(e[i]);return t}function b(e,t){return function(){e.apply(t,arguments)}}function C(e){return"function"==typeof e}return n&&(t.onmessage=function(e){var i=e.data;if(void 0===o.WORKER_ID&&i&&(o.WORKER_ID=i.workerId),"string"==typeof i.input)t.postMessage({workerId:o.WORKER_ID,results:o.parse(i.input,i.config),finished:!0});else if(t.File&&i.input instanceof File||i.input instanceof Object){var r=o.parse(i.input,i.config);r&&t.postMessage({workerId:o.WORKER_ID,results:r,finished:!0})}}),f.prototype=Object.create(u.prototype),f.prototype.constructor=f,d.prototype=Object.create(u.prototype),d.prototype.constructor=d,l.prototype=Object.create(l.prototype),l.prototype.constructor=l,c.prototype=Object.create(u.prototype),c.prototype.constructor=c,"undefined"==typeof PAPA_BROWSER_CONTEXT&&(p.prototype=Object.create(u.prototype),p.prototype.constructor=p),o})?r.apply(t,[]):r)||(e.exports=n)}};