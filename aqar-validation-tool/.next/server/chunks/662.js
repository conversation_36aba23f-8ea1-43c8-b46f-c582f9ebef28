exports.id=662,exports.ids=[662],exports.modules={453:(e,t,i)=>{"use strict";i.d(t,{bK:()=>r,iv:()=>n});let n=[{institute_code:"101",institute_short_name:"SLS-P",institute_name:"Symbiosis Law School, Pune",faculty:"LAW",city:"Pune"},{institute_code:"102",institute_short_name:"SLS-N",institute_name:"Symbiosis Law School, Noida",faculty:"LAW",city:"Noida"},{institute_code:"103",institute_short_name:"SLS-H",institute_name:"Symbiosis Law School, Hyderabad",faculty:"LAW",city:"Hyderabad"},{institute_code:"104",institute_short_name:"SLS-NG",institute_name:"Symbiosis Law School, Nagpur",faculty:"LAW",city:"Nagpur"},{institute_code:"201",institute_short_name:"SIBM-P",institute_name:"Symbiosis Institute of Business Management, Pune",faculty:"MANAGEMENT",city:"Pune"},{institute_code:"202",institute_short_name:"SIIB",institute_name:"Symbiosis Institute of International Business",faculty:"MANAGEMENT",city:"Pune"},{institute_code:"203",institute_short_name:"SIBM-B",institute_name:"Symbiosis Institute of Business Management, Bengaluru",faculty:"MANAGEMENT",city:"Bengaluru"},{institute_code:"204",institute_short_name:"SIBM-H",institute_name:"Symbiosis Institute of Business Management, Hyderabad",faculty:"MANAGEMENT",city:"Hyderabad"},{institute_code:"205",institute_short_name:"SIBM-N",institute_name:"Symbiosis Institute of Business Management, Nagpur",faculty:"MANAGEMENT",city:"Nagpur"},{institute_code:"206",institute_short_name:"SCMHRD",institute_name:"Symbiosis Centre for Management and Human Resource Development",faculty:"MANAGEMENT",city:"Pune"},{institute_code:"301",institute_short_name:"SIT",institute_name:"Symbiosis Institute of Technology",faculty:"ENGINEERING",city:"Pune"},{institute_code:"302",institute_short_name:"SITB",institute_name:"Symbiosis Institute of Technology, Bengaluru",faculty:"ENGINEERING",city:"Bengaluru"},{institute_code:"303",institute_short_name:"SITH",institute_name:"Symbiosis Institute of Technology, Hyderabad",faculty:"ENGINEERING",city:"Hyderabad"},{institute_code:"304",institute_short_name:"SITN",institute_name:"Symbiosis Institute of Technology, Nagpur",faculty:"ENGINEERING",city:"Nagpur"},{institute_code:"401",institute_short_name:"SICSR",institute_name:"Symbiosis Institute of Computer Studies and Research",faculty:"COMPUTER_SCIENCE",city:"Pune"},{institute_code:"402",institute_short_name:"SICSR-N",institute_name:"Symbiosis Institute of Computer Studies and Research, Nagpur",faculty:"COMPUTER_SCIENCE",city:"Nagpur"},{institute_code:"501",institute_short_name:"SIMC",institute_name:"Symbiosis Institute of Media and Communication",faculty:"MEDIA",city:"Pune"},{institute_code:"502",institute_short_name:"SIMC-B",institute_name:"Symbiosis Institute of Media and Communication, Bengaluru",faculty:"MEDIA",city:"Bengaluru"},{institute_code:"601",institute_short_name:"SID",institute_name:"Symbiosis Institute of Design",faculty:"DESIGN",city:"Pune"},{institute_code:"701",institute_short_name:"SIU-CON",institute_name:"Symbiosis College of Nursing",faculty:"HEALTH_SCIENCES",city:"Pune"},{institute_code:"702",institute_short_name:"SIHSRC",institute_name:"Symbiosis Institute of Health Sciences Research Centre",faculty:"HEALTH_SCIENCES",city:"Pune"},{institute_code:"801",institute_short_name:"SITM",institute_name:"Symbiosis Institute of Telecom Management",faculty:"TELECOM",city:"Pune"},{institute_code:"901",institute_short_name:"SIOM",institute_name:"Symbiosis Institute of Operations Management",faculty:"OPERATIONS",city:"Nashik"},{institute_code:"1001",institute_short_name:"SIS",institute_name:"Symbiosis Statistical Institute",faculty:"STATISTICS",city:"Pune"},{institute_code:"1101",institute_short_name:"SSE",institute_name:"Symbiosis School of Economics",faculty:"ECONOMICS",city:"Pune"},{institute_code:"1201",institute_short_name:"SSBF",institute_name:"Symbiosis School of Banking and Finance",faculty:"FINANCE",city:"Pune"},{institute_code:"1301",institute_short_name:"SSLA",institute_name:"Symbiosis School of Liberal Arts",faculty:"LIBERAL_ARTS",city:"Pune"},{institute_code:"1401",institute_short_name:"SICA",institute_name:"Symbiosis Institute of Culinary Arts",faculty:"CULINARY",city:"Pune"},{institute_code:"1501",institute_short_name:"SICG",institute_name:"Symbiosis Institute of Geoinformatics",faculty:"GEOINFORMATICS",city:"Pune"},{institute_code:"1601",institute_short_name:"SSIIS",institute_name:"Symbiosis School of International Studies",faculty:"INTERNATIONAL_STUDIES",city:"Pune"},{institute_code:"1701",institute_short_name:"SSSS",institute_name:"Symbiosis School of Sports Sciences",faculty:"SPORTS",city:"Pune"},{institute_code:"1801",institute_short_name:"SSOP",institute_name:"Symbiosis School of Photography",faculty:"PHOTOGRAPHY",city:"Pune"},{institute_code:"1901",institute_short_name:"SSPAD",institute_name:"Symbiosis School of Planning Architecture and Design",faculty:"ARCHITECTURE",city:"Pune"},{institute_code:"2001",institute_short_name:"SSBS",institute_name:"Symbiosis School of Biological Sciences",faculty:"BIOLOGICAL_SCIENCES",city:"Pune"},{institute_code:"2101",institute_short_name:"SSPA",institute_name:"Symbiosis School of Performing Arts",faculty:"PERFORMING_ARTS",city:"Pune"},{institute_code:"2201",institute_short_name:"SSPU",institute_name:"Symbiosis Skills and Professional University",faculty:"SKILLS",city:"Pune"},{institute_code:"2301",institute_short_name:"SCDL",institute_name:"Symbiosis Centre for Distance Learning",faculty:"DISTANCE_LEARNING",city:"Pune"},{institute_code:"2401",institute_short_name:"SCIT",institute_name:"Symbiosis Centre for Information Technology",faculty:"INFORMATION_TECHNOLOGY",city:"Pune"},{institute_code:"2501",institute_short_name:"SCRI",institute_name:"Symbiosis Centre for Research and Innovation",faculty:"RESEARCH",city:"Pune"},{institute_code:"2601",institute_short_name:"SIS-P",institute_name:"Symbiosis International School, Pune",faculty:"SCHOOL",city:"Pune"},{institute_code:"2701",institute_short_name:"SCCE",institute_name:"Symbiosis Centre for Corporate Education",faculty:"CORPORATE_EDUCATION",city:"Pune"},{institute_code:"2801",institute_short_name:"SIFIL",institute_name:"Symbiosis Institute of Foreign and Indian Languages",faculty:"LANGUAGES",city:"Pune"},{institute_code:"2901",institute_short_name:"SCMS-P",institute_name:"Symbiosis Centre for Management Studies, Pune",faculty:"MANAGEMENT",city:"Pune"},{institute_code:"2902",institute_short_name:"SCMS-N",institute_name:"Symbiosis Centre for Management Studies, Noida",faculty:"MANAGEMENT",city:"Noida"}],r=e=>n.find(t=>t.institute_code===e)},2178:(e,t,i)=>{"use strict";i.d(t,{a:()=>r});var n=i(1437);class r{static{this.STORAGE_KEY="aqar_submissions"}static{this.MAX_FILE_SIZE=0xa00000}static{this.ALLOWED_TYPES=[".csv"]}static async uploadFile(e,t,i){try{let n=this.validateFile(e);if(!n.isValid)return{success:!1,fileName:e.name,filePath:"",fileSize:e.size,error:n.error};let r=new Date().toISOString().replace(/[:.]/g,"-"),s=`${t}_${i}_${r}_${e.name}`,a=`uploads/${s}`;return await this.simulateFileUpload(e,a),{success:!0,fileName:e.name,filePath:a,fileSize:e.size}}catch(t){return{success:!1,fileName:e.name,filePath:"",fileSize:e.size,error:t instanceof Error?t.message:"Upload failed"}}}static validateFile(e){if(e.size>this.MAX_FILE_SIZE)return{isValid:!1,error:`File size exceeds ${this.MAX_FILE_SIZE/1048576}MB limit`};let t="."+e.name.split(".").pop()?.toLowerCase();return this.ALLOWED_TYPES.includes(t)?0===e.size?{isValid:!1,error:"File cannot be empty"}:{isValid:!0}:{isValid:!1,error:`File type not allowed. Allowed types: ${this.ALLOWED_TYPES.join(", ")}`}}static async simulateFileUpload(e,t){return new Promise(e=>{setTimeout(()=>{e()},1e3)})}static saveSubmission(e,t,i,n,r,s){let a={id:this.generateSubmissionId(),timestamp:new Date,institute_code:e,institute_name:t,metric_number:i,validation_status:n.isValid?"passed":"failed",error_count:n.errorCount,warning_count:n.warningCount,file_name:`${e}_${i}_${new Date().toISOString().split("T")[0]}.csv`,original_file_name:s,submitted_to_qmb:!1,file_path:r,validation_result:n},o=this.getSubmissions();return o.push(a),localStorage.setItem(this.STORAGE_KEY,JSON.stringify(o)),a}static getSubmissions(){try{let e=localStorage.getItem(this.STORAGE_KEY);if(!e)return[];return JSON.parse(e).map(e=>({...e,timestamp:new Date(e.timestamp),validation_result:e.validation_result?{...e.validation_result,timestamp:new Date(e.validation_result.timestamp)}:void 0}))}catch{return[]}}static getSubmissionsByInstitute(e){return this.getSubmissions().filter(t=>t.institute_code===e)}static getSubmissionsByMetric(e){return this.getSubmissions().filter(t=>t.metric_number===e)}static markAsSubmittedToQMB(e){try{let t=this.getSubmissions(),i=t.findIndex(t=>t.id===e);if(-1===i)return!1;return t[i].submitted_to_qmb=!0,localStorage.setItem(this.STORAGE_KEY,JSON.stringify(t)),!0}catch{return!1}}static generateSubmissionId(){return`SUB_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}static generateErrorReport(e,t,i,n){return{reportId:`ERR_${Date.now()}`,instituteCode:t,instituteName:i,metricNumber:n,generatedAt:new Date,errors:e.validation_result?.errors||[],summary:e.validation_result?.summary||{totalRows:0,validRows:0,errorRows:0,warningRows:0,criticalErrors:[],commonErrors:{}},recommendations:this.generateRecommendations(e.validation_result?.errors||[])}}static downloadErrorReport(e){let t=n.Wp.book_new(),i=[["NAAC AQAR Validation Error Report"],[""],["Report ID",e.reportId],["Institute Code",e.instituteCode],["Institute Name",e.instituteName],["Metric Number",e.metricNumber],["Generated At",e.generatedAt.toLocaleString()],[""],["Summary"],["Total Rows",e.summary.totalRows],["Valid Rows",e.summary.validRows],["Error Rows",e.summary.errorRows],["Warning Rows",e.summary.warningRows],[""],["Common Errors"],...Object.entries(e.summary.commonErrors).map(([e,t])=>[e,t])],r=n.Wp.aoa_to_sheet(i);n.Wp.book_append_sheet(t,r,"Summary");let s=[["Row","Column","Value","Error","Rule","Severity"],...e.errors.map(e=>[e.row,e.column,e.value,e.error,e.rule,e.severity])],a=n.Wp.aoa_to_sheet(s);n.Wp.book_append_sheet(t,a,"Errors");let o=[["Recommendations"],[""],...e.recommendations.map(e=>[e])],u=n.Wp.aoa_to_sheet(o);n.Wp.book_append_sheet(t,u,"Recommendations");let c=`AQAR_Error_Report_${e.instituteCode}_${e.metricNumber}_${new Date().toISOString().split("T")[0]}.xlsx`;n._h(t,c)}static generateRecommendations(e){let t=[];return new Set(e.map(e=>e.rule)).forEach(e=>{switch(e){case"required_field":t.push("Ensure all required fields are filled. Check the template for mandatory columns.");break;case"year_range":t.push("Verify that all year entries are within the specified range (typically 2019-2024).");break;case"numeric":t.push("Check that numeric fields contain only numbers without any text or special characters.");break;case"institute_code_choice":t.push("Use only valid SIU institute codes. Refer to the institution list provided.");break;case"date":t.push("Ensure dates are in DD/MM/YYYY format and represent valid dates.");break;case"word_limit":t.push("Review text fields that exceed word limits and provide concise descriptions.");break;case"exact_match":t.push("Use only the specified values for choice fields. Check the template for valid options.");break;case"url_pattern":t.push("Ensure URLs are properly formatted and start with http:// or https://");break;case"currency":t.push("Enter currency amounts as numbers only, without currency symbols or commas.");break;default:t.push(`Review and correct ${e} validation errors according to the field requirements.`)}}),t.push(""),t.push("General Recommendations:"),t.push("1. Download and use the latest CSV template for your metric"),t.push("2. Do not modify column headers in the template"),t.push("3. Ensure data consistency across all rows"),t.push("4. Validate your data before final submission"),t.push("5. Contact QMB team for clarification if needed: <EMAIL>"),t}static getSubmissionStatistics(){let e=this.getSubmissions(),t={totalSubmissions:e.length,passedValidations:e.filter(e=>"passed"===e.validation_status).length,failedValidations:e.filter(e=>"failed"===e.validation_status).length,submittedToQMB:e.filter(e=>e.submitted_to_qmb).length,byInstitute:{},byMetric:{},recentSubmissions:e.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()).slice(0,10)};return e.forEach(e=>{t.byInstitute[e.institute_code]=(t.byInstitute[e.institute_code]||0)+1}),e.forEach(e=>{t.byMetric[e.metric_number]=(t.byMetric[e.metric_number]||0)+1}),t}static clearAllSubmissions(){localStorage.removeItem(this.STORAGE_KEY)}static exportSubmissionsToExcel(){let e=[["Submission ID","Timestamp","Institute Code","Institute Name","Metric Number","Validation Status","Error Count","Warning Count","File Name","Submitted to QMB"],...this.getSubmissions().map(e=>[e.id,e.timestamp.toLocaleString(),e.institute_code,e.institute_name,e.metric_number,e.validation_status,e.error_count,e.warning_count,e.original_file_name,e.submitted_to_qmb?"Yes":"No"])],t=n.Wp.book_new(),i=n.Wp.aoa_to_sheet(e);n.Wp.book_append_sheet(t,i,"Submissions");let r=`AQAR_Submissions_${new Date().toISOString().split("T")[0]}.xlsx`;n._h(t,r)}}},2869:(e,t,i)=>{"use strict";i.d(t,{Cb:()=>n,pF:()=>r});let n=[{metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",criteria_number:"1.1"},{metric_number:"1.1.1a",metric_description:"Global developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.1b",metric_description:"National developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.1c",metric_description:"Regional developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.1d",metric_description:"Local developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.3",metric_description:"Percentage of students undertaking project work/field work/internships",criteria_number:"1.1"},{metric_number:"1.3.1",metric_description:"Institution integrates crosscutting issues relevant to Professional Ethics, Gender, Human Values, Environment and Sustainability into the Curriculum",criteria_number:"1.3"},{metric_number:"1.3.1a",metric_description:"Number of courses that include focus on gender issues during the last five years",criteria_number:"1.3"},{metric_number:"1.3.2a",metric_description:"Number of value-added courses for imparting transferable and life skills offered during last five years",criteria_number:"1.3"},{metric_number:"1.3.2b",metric_description:"Number of students enrolled in the courses under 1.3.2 above",criteria_number:"1.3"},{metric_number:"1.3.4",metric_description:"Percentage of students undertaking field projects/internships/student projects",criteria_number:"1.3"},{metric_number:"1.4.1",metric_description:"Institution obtains feedback on the syllabus and its transaction at the institution from the following stakeholders",criteria_number:"1.4"},{metric_number:"1.4.2",metric_description:"Feedback process of the Institution may be classified as follows",criteria_number:"1.4"},{metric_number:"2.2.1",metric_description:"Student – Full time Teacher Ratio",criteria_number:"2.2"},{metric_number:"2.3.1",metric_description:"Student centric methods, such as experiential learning, participative learning and problem solving methodologies are used for enhancing learning experiences",criteria_number:"2.3"},{metric_number:"2.3.2",metric_description:"Teachers use ICT enabled tools for effective teaching-learning process",criteria_number:"2.3"},{metric_number:"2.3.3",metric_description:"Ratio of students to mentor for academic and other related issues",criteria_number:"2.3"},{metric_number:"2.4.4",metric_description:"Percentage of full time teachers against sanctioned posts during the last five years",criteria_number:"2.4"},{metric_number:"2.6.1",metric_description:"Programme and course outcomes for all Programmes offered by the institution are stated and displayed on website and communicated to teachers and students",criteria_number:"2.6"},{metric_number:"2.6.2",metric_description:"Attainment of Programme outcomes and course outcomes are evaluated by the institution",criteria_number:"2.6"},{metric_number:"3.1.5",metric_description:"Institution has the following facilities for research",criteria_number:"3.1"},{metric_number:"3.3.2",metric_description:"Number of workshops/seminars conducted on Research Methodology, Intellectual Property Rights (IPR) and entrepreneurship during the last five years",criteria_number:"3.3"},{metric_number:"3.3.3",metric_description:"Number of awards/recognitions received for extension activities from government/government recognised bodies during the last five years",criteria_number:"3.3"},{metric_number:"3.4.7",metric_description:"Number of registered patents (published/not published) and patents (Granted) during the last five years",criteria_number:"3.4"},{metric_number:"3.6.1",metric_description:"Extension activities are carried out in the neighborhood community, sensitizing students to social issues, for their holistic development, and impact thereof during the last five years",criteria_number:"3.6"},{metric_number:"3.6.2",metric_description:"Number of awards and recognitions received for extension activities from government/government recognised bodies during the last five years",criteria_number:"3.6"},{metric_number:"3.6.3",metric_description:"Number of extension and outreach programs conducted by the institution through NSS/NCC/Red cross/YRC etc., during the last five years",criteria_number:"3.6"},{metric_number:"3.6.4",metric_description:"Average percentage of students participating in extension activities at 3.6.3 above during last five years",criteria_number:"3.6"},{metric_number:"3.7.1",metric_description:"Number of Collaborative activities for research, Faculty exchange, Student exchange/internship during the last five years",criteria_number:"3.7"},{metric_number:"4.1.1",metric_description:"The Institution has adequate infrastructure and physical facilities for teaching-learning. viz., classrooms, laboratories, computing equipment etc.",criteria_number:"4.1"},{metric_number:"4.3.3",metric_description:"Bandwidth of internet connection in the Institution",criteria_number:"4.3"},{metric_number:"4.3.5",metric_description:"Institution has the following Facilities for e-content development",criteria_number:"4.3"},{metric_number:"5.1.2",metric_description:"Number of capability enhancement and development schemes such as Soft skill development, Remedial coaching, Language lab, Bridge courses, Yoga, Meditation, Personal Counselling and Mentoring etc., for students",criteria_number:"5.1"},{metric_number:"5.1.3a",metric_description:"Number of students benefitted by guidance for competitive examinations and career counselling offered by the Institution during the last five years",criteria_number:"5.1"},{metric_number:"5.1.3b",metric_description:"Number of students who have passed in the qualifying examination conducted by national/state/government or other recognized bodies during the last five years",criteria_number:"5.1"},{metric_number:"5.2.1",metric_description:"Average percentage of placement of outgoing students during the last five years",criteria_number:"5.2"},{metric_number:"5.2.2",metric_description:"Average percentage of students progressing to higher education during the last five years",criteria_number:"5.2"},{metric_number:"5.2.3",metric_description:"Average percentage of students qualifying in state/national/international level examinations during the last five years",criteria_number:"5.2"},{metric_number:"5.3.1",metric_description:"Number of awards/medals won by students for outstanding performance in sports/cultural activities at inter-university/state/national/international level during the last five years",criteria_number:"5.3"},{metric_number:"5.3.2",metric_description:"Institution facilitates students representation and engagement in various administrative, co-curricular and extracurricular activities",criteria_number:"5.3"},{metric_number:"5.3.3",metric_description:"Average number of sports and cultural programs in which students of the Institution participated during last five years",criteria_number:"5.3"},{metric_number:"5.4.1",metric_description:"There is a registered Alumni Association that contributes significantly to the development of the institution through financial and/or other support services",criteria_number:"5.4"},{metric_number:"6.3.3",metric_description:"Average number of professional development/administrative training programs organized by the institution for teaching and non-teaching staff during the last five years",criteria_number:"6.3"},{metric_number:"6.3.4",metric_description:"Average percentage of teachers undergoing online/face-to-face Faculty Development Programmes (FDP) during the last five years",criteria_number:"6.3"},{metric_number:"6.5.2",metric_description:"The institution reviews its teaching learning process, structures & methodologies of operations and learning outcomes at periodic intervals through IQAC set up as per norms and recorded the incremental improvement in various activities",criteria_number:"6.5"},{metric_number:"7.1.1",metric_description:"Measures initiated by the Institution for the promotion of gender equity during the last five years",criteria_number:"7.1"},{metric_number:"7.1.8",metric_description:"Average percentage of expenditure for infrastructure augmentation excluding salary during the last five years",criteria_number:"7.1"},{metric_number:"7.1.9",metric_description:"Sensitization of students and employees of the Institution to the constitutional obligations: values, rights, duties and responsibilities of citizens",criteria_number:"7.1"},{metric_number:"7.1.10",metric_description:"The Institution has a prescribed code of conduct for students, teachers, administrators and other staff and conducts periodic programmes in this regard",criteria_number:"7.1"},{metric_number:"7.1.11",metric_description:"Institution celebrates/organizes national and international commemorative days, events and festivals",criteria_number:"7.1"},{metric_number:"7.2.1",metric_description:"Describe two best practices successfully implemented by the Institution as per NAAC format provided in the Manual",criteria_number:"7.2"},{metric_number:"7.3.1",metric_description:"Portray the performance of the Institution in one area distinctive to its priority and thrust within a maximum of 500 words",criteria_number:"7.3"}],r=e=>n.find(t=>t.metric_number===e)},6487:()=>{},8335:()=>{}};