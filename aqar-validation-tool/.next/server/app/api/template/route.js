(()=>{var e={};e.id=264,e.ids=[264],e.modules={634:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>b,routeModule:()=>_,serverHooks:()=>g,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>f});var i={};r.r(i),r.d(i,{GET:()=>d,POST:()=>p});var n=r(6559),a=r(8088),s=r(7719),o=r(2190),c=r(1670),u=r(2869),m=r(4537);class l{static generateTemplate(e){let t=(0,u.pF)(e),r=(0,c.id)(e);if(!t)throw Error(`Metric ${e} not found`);let i=this.generateTemplateColumns(r),n=this.generateSampleData(i),a=this.generateInstructions(e,r);return{metricNumber:e,metricDescription:t.metric_description,columns:i,instructions:a,sampleData:n}}static generateTemplateColumns(e){let t=new Map;return e.forEach(e=>{t.has(e.column_name)||t.set(e.column_name,{name:e.column_name,description:this.getColumnDescription(e),dataType:e.data_type,required:e.is_required,validationHint:this.getValidationHint(e),sampleValue:this.generateSampleValue(e)})}),Array.from(t.values()).sort((e,t)=>e.required&&!t.required?-1:!e.required&&t.required?1:e.name.localeCompare(t.name))}static getColumnDescription(e){return({Programme_Name:"Name of the academic programme",Academic_Year:"Academic year in YYYY-YYYY format",Institute_Code:"Official institute code from SIU",Institute_Name:"Full name of the institute",Faculty:"Faculty/Department name",Total_Students:"Total number of students",Students_With_Projects:"Number of students with projects/internships",Global_Need_Description:"Description of global developmental needs addressed",National_Need_Description:"Description of national developmental needs addressed",Evidence_Document:"Supporting document file path",Professional_Ethics:"Whether professional ethics is integrated (Yes/No)",Gender_Issues:"Whether gender issues are addressed (Yes/No)",Workshop_Title:"Title of the workshop/seminar",Date_Conducted:"Date when workshop was conducted",Bandwidth_Mbps:"Internet bandwidth in Mbps",Service_Provider:"Internet service provider name",Students_Eligible_For_Placement:"Number of students eligible for placement",Students_Placed:"Number of students successfully placed",FDP_Type:"Type of Faculty Development Programme",Duration_Hours:"Duration of programme in hours",Infrastructure_Expenditure:"Amount spent on infrastructure",Total_Expenditure:"Total expenditure amount",Remarks:"Additional remarks or comments",Supporting_Document_URL:"URL to supporting documents"})[e.column_name]||`Data for ${e.column_name.replace(/_/g," ")}`}static getValidationHint(e){switch(e.validation_type){case"year_range":return`Year between ${e.validation_params}`;case"exact_match":return`Must be one of: ${e.validation_params}`;case"numeric":return`Numeric value ${e.validation_params?`(${e.validation_params})`:""}`;case"word_limit":return`Maximum ${e.validation_params} words`;case"text_length":return`Text length ${e.validation_params}`;case"yes_no_choice":return"Enter Yes or No";case"date":return"Date in DD/MM/YYYY format";case"currency":return"Currency amount (e.g., 100000)";case"url_pattern":return"Valid URL starting with http:// or https://";case"pdf_file":return"PDF file path or name";case"image_file":return"Image file path (jpg, png, etc.)";case"required_field":return"This field is mandatory";case"institute_code_choice":return"Valid SIU institute code";case"institute_name_choice":return"Valid SIU institute name";case"faculty_choice":return"Valid faculty name";default:return"Follow the specified format"}}static generateSampleValue(e){switch(e.validation_type){case"year_range":let[t]=e.validation_params.split("-");return t||"2023";case"exact_match":let r=e.validation_params.split(",");return r[0]?.trim()||"Option1";case"numeric":return"100";case"word_limit":return"Sample text content";case"yes_no_choice":return"Yes";case"date":return"01/01/2023";case"currency":return"100000";case"url_pattern":return"https://example.com/document.pdf";case"pdf_file":return"document.pdf";case"image_file":return"image.jpg";case"institute_code_choice":return"101";case"institute_name_choice":return"Symbiosis Law School, Pune";case"faculty_choice":return"LAW";default:return this.getDefaultSampleValue(e.column_name)}}static getDefaultSampleValue(e){return({Programme_Name:"Bachelor of Laws (LL.B.)",Academic_Year:"2023-2024",Institute_Code:"101",Institute_Name:"Symbiosis Law School, Pune",Faculty:"LAW",Total_Students:"150",Students_With_Projects:"120",Workshop_Title:"Research Methodology Workshop",Service_Provider:"Airtel Business",Bandwidth_Mbps:"100",Remarks:"Additional information if any"})[e]||"Sample Value"}static generateSampleData(e){let t=[];for(let r=0;r<3;r++){let i=e.map(e=>"Academic_Year"===e.name?`${2021+r}-${2022+r}`:e.name.includes("Total")||e.name.includes("Students")?(100+10*r).toString():e.sampleValue);t.push(i)}return t}static generateInstructions(e,t){let r=[`Template for Metric ${e}`,"Please follow these guidelines when filling the template:","","General Instructions:","• Fill all required fields (marked with *)","• Use the exact format specified for each field","• Do not modify column headers","• Ensure data consistency across all rows","• Save the file in CSV format before uploading","","Field-specific Instructions:"];return[...new Set(t.map(e=>e.validation_type))].forEach(e=>{switch(e){case"year_range":r.push("• Academic years should be in YYYY-YYYY format (e.g., 2023-2024)");break;case"institute_code_choice":r.push("• Use only valid SIU institute codes (101, 102, 201, etc.)");break;case"date":r.push("• Dates should be in DD/MM/YYYY format (e.g., 15/03/2023)");break;case"numeric":r.push("• Numeric fields should contain only numbers");break;case"currency":r.push("• Currency amounts should be in numbers only (without symbols)");break;case"yes_no_choice":r.push('• Yes/No fields should contain exactly "Yes" or "No"');break;case"word_limit":r.push("• Respect word limits for text fields");break;case"pdf_file":r.push("• File references should include .pdf extension")}}),r.push(""),r.push("For support, contact: <EMAIL>"),r}static downloadTemplate(e){let t=this.generateTemplate(e),r=t.columns.map(e=>e.name),i=m.N.convertToCSV(r,t.sampleData),n=`AQAR_Template_${e}_${new Date().toISOString().split("T")[0]}.csv`;m.N.downloadCSV(i,n)}static generateTemplateWithInstructions(e){let t=this.generateTemplate(e),r="";t.instructions.forEach(e=>{r+=`# ${e}
`}),r+="\n",r+="# Column Descriptions:\n",t.columns.forEach(e=>{let t=e.required?" (Required)":" (Optional)";r+=`# ${e.name}${t}: ${e.description}
`,r+=`#   Format: ${e.validationHint}
`,r+=`#   Example: ${e.sampleValue}
`}),r+="\n";let i=t.columns.map(e=>e.name),n=m.N.convertToCSV(i,t.sampleData);return r+=n}static getTemplateSummary(e){let t=this.generateTemplate(e),r=t.columns.filter(e=>e.required).length,i=[...new Set(t.columns.map(e=>e.validationHint.split(" ")[0]))];return{metricNumber:t.metricNumber,metricDescription:t.metricDescription,totalColumns:t.columns.length,requiredColumns:r,optionalColumns:t.columns.length-r,validationTypes:i}}}async function d(e){try{let t,{searchParams:r}=new URL(e.url),i=r.get("metricNumber"),n=r.get("format")||"csv",a="true"===r.get("includeInstructions");if(!i)return o.NextResponse.json({success:!1,error:"Missing required parameter: metricNumber"},{status:400});if(!(0,u.pF)(i))return o.NextResponse.json({success:!1,error:`Invalid metric number: ${i}`},{status:400});let s=l.generateTemplate(i);if("json"===n)return o.NextResponse.json({success:!0,data:s,message:`Template generated for metric ${i}`});if(a)t=l.generateTemplateWithInstructions(i);else{let e=s.columns.map(e=>e.name);t=m.N.convertToCSV(e,s.sampleData)}let c=`AQAR_Template_${i}_${new Date().toISOString().split("T")[0]}.csv`;return new o.NextResponse(t,{status:200,headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="${c}"`,"Cache-Control":"no-cache"}})}catch(e){return console.error("Template API error:",e),o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Internal server error",message:"Failed to generate template"},{status:500})}}async function p(e){try{let{metricNumbers:t,format:r="zip"}=await e.json();if(!t||!Array.isArray(t)||0===t.length)return o.NextResponse.json({success:!1,error:"Missing or invalid metricNumbers array"},{status:400});let i=t.filter(e=>!(0,u.pF)(e));if(i.length>0)return o.NextResponse.json({success:!1,error:`Invalid metric numbers: ${i.join(", ")}`},{status:400});let n=t.map(e=>{let t=l.generateTemplate(e),r=t.columns.map(e=>e.name),i=m.N.convertToCSV(r,t.sampleData);return{metricNumber:e,fileName:`AQAR_Template_${e}.csv`,content:i,template:t}});if("json"===r)return o.NextResponse.json({success:!0,data:n,message:`Generated ${n.length} templates`});let a=n[0],s=`AQAR_Templates_${new Date().toISOString().split("T")[0]}.csv`;return new o.NextResponse(a.content,{status:200,headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="${s}"`,"Cache-Control":"no-cache"}})}catch(e){return console.error("Bulk template API error:",e),o.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Internal server error",message:"Failed to generate templates"},{status:500})}}let _=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/template/route",pathname:"/api/template",filename:"route",bundlePath:"app/api/template/route"},resolvedPagePath:"/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/api/template/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:h,workUnitAsyncStorage:f,serverHooks:g}=_;function b(){return(0,s.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:f})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2869:(e,t,r)=>{"use strict";r.d(t,{Cb:()=>i,pF:()=>n});let i=[{metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",criteria_number:"1.1"},{metric_number:"1.1.1a",metric_description:"Global developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.1b",metric_description:"National developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.1c",metric_description:"Regional developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.1d",metric_description:"Local developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.3",metric_description:"Percentage of students undertaking project work/field work/internships",criteria_number:"1.1"},{metric_number:"1.3.1",metric_description:"Institution integrates crosscutting issues relevant to Professional Ethics, Gender, Human Values, Environment and Sustainability into the Curriculum",criteria_number:"1.3"},{metric_number:"1.3.1a",metric_description:"Number of courses that include focus on gender issues during the last five years",criteria_number:"1.3"},{metric_number:"1.3.2a",metric_description:"Number of value-added courses for imparting transferable and life skills offered during last five years",criteria_number:"1.3"},{metric_number:"1.3.2b",metric_description:"Number of students enrolled in the courses under 1.3.2 above",criteria_number:"1.3"},{metric_number:"1.3.4",metric_description:"Percentage of students undertaking field projects/internships/student projects",criteria_number:"1.3"},{metric_number:"1.4.1",metric_description:"Institution obtains feedback on the syllabus and its transaction at the institution from the following stakeholders",criteria_number:"1.4"},{metric_number:"1.4.2",metric_description:"Feedback process of the Institution may be classified as follows",criteria_number:"1.4"},{metric_number:"2.2.1",metric_description:"Student – Full time Teacher Ratio",criteria_number:"2.2"},{metric_number:"2.3.1",metric_description:"Student centric methods, such as experiential learning, participative learning and problem solving methodologies are used for enhancing learning experiences",criteria_number:"2.3"},{metric_number:"2.3.2",metric_description:"Teachers use ICT enabled tools for effective teaching-learning process",criteria_number:"2.3"},{metric_number:"2.3.3",metric_description:"Ratio of students to mentor for academic and other related issues",criteria_number:"2.3"},{metric_number:"2.4.4",metric_description:"Percentage of full time teachers against sanctioned posts during the last five years",criteria_number:"2.4"},{metric_number:"2.6.1",metric_description:"Programme and course outcomes for all Programmes offered by the institution are stated and displayed on website and communicated to teachers and students",criteria_number:"2.6"},{metric_number:"2.6.2",metric_description:"Attainment of Programme outcomes and course outcomes are evaluated by the institution",criteria_number:"2.6"},{metric_number:"3.1.5",metric_description:"Institution has the following facilities for research",criteria_number:"3.1"},{metric_number:"3.3.2",metric_description:"Number of workshops/seminars conducted on Research Methodology, Intellectual Property Rights (IPR) and entrepreneurship during the last five years",criteria_number:"3.3"},{metric_number:"3.3.3",metric_description:"Number of awards/recognitions received for extension activities from government/government recognised bodies during the last five years",criteria_number:"3.3"},{metric_number:"3.4.7",metric_description:"Number of registered patents (published/not published) and patents (Granted) during the last five years",criteria_number:"3.4"},{metric_number:"3.6.1",metric_description:"Extension activities are carried out in the neighborhood community, sensitizing students to social issues, for their holistic development, and impact thereof during the last five years",criteria_number:"3.6"},{metric_number:"3.6.2",metric_description:"Number of awards and recognitions received for extension activities from government/government recognised bodies during the last five years",criteria_number:"3.6"},{metric_number:"3.6.3",metric_description:"Number of extension and outreach programs conducted by the institution through NSS/NCC/Red cross/YRC etc., during the last five years",criteria_number:"3.6"},{metric_number:"3.6.4",metric_description:"Average percentage of students participating in extension activities at 3.6.3 above during last five years",criteria_number:"3.6"},{metric_number:"3.7.1",metric_description:"Number of Collaborative activities for research, Faculty exchange, Student exchange/internship during the last five years",criteria_number:"3.7"},{metric_number:"4.1.1",metric_description:"The Institution has adequate infrastructure and physical facilities for teaching-learning. viz., classrooms, laboratories, computing equipment etc.",criteria_number:"4.1"},{metric_number:"4.3.3",metric_description:"Bandwidth of internet connection in the Institution",criteria_number:"4.3"},{metric_number:"4.3.5",metric_description:"Institution has the following Facilities for e-content development",criteria_number:"4.3"},{metric_number:"5.1.2",metric_description:"Number of capability enhancement and development schemes such as Soft skill development, Remedial coaching, Language lab, Bridge courses, Yoga, Meditation, Personal Counselling and Mentoring etc., for students",criteria_number:"5.1"},{metric_number:"5.1.3a",metric_description:"Number of students benefitted by guidance for competitive examinations and career counselling offered by the Institution during the last five years",criteria_number:"5.1"},{metric_number:"5.1.3b",metric_description:"Number of students who have passed in the qualifying examination conducted by national/state/government or other recognized bodies during the last five years",criteria_number:"5.1"},{metric_number:"5.2.1",metric_description:"Average percentage of placement of outgoing students during the last five years",criteria_number:"5.2"},{metric_number:"5.2.2",metric_description:"Average percentage of students progressing to higher education during the last five years",criteria_number:"5.2"},{metric_number:"5.2.3",metric_description:"Average percentage of students qualifying in state/national/international level examinations during the last five years",criteria_number:"5.2"},{metric_number:"5.3.1",metric_description:"Number of awards/medals won by students for outstanding performance in sports/cultural activities at inter-university/state/national/international level during the last five years",criteria_number:"5.3"},{metric_number:"5.3.2",metric_description:"Institution facilitates students representation and engagement in various administrative, co-curricular and extracurricular activities",criteria_number:"5.3"},{metric_number:"5.3.3",metric_description:"Average number of sports and cultural programs in which students of the Institution participated during last five years",criteria_number:"5.3"},{metric_number:"5.4.1",metric_description:"There is a registered Alumni Association that contributes significantly to the development of the institution through financial and/or other support services",criteria_number:"5.4"},{metric_number:"6.3.3",metric_description:"Average number of professional development/administrative training programs organized by the institution for teaching and non-teaching staff during the last five years",criteria_number:"6.3"},{metric_number:"6.3.4",metric_description:"Average percentage of teachers undergoing online/face-to-face Faculty Development Programmes (FDP) during the last five years",criteria_number:"6.3"},{metric_number:"6.5.2",metric_description:"The institution reviews its teaching learning process, structures & methodologies of operations and learning outcomes at periodic intervals through IQAC set up as per norms and recorded the incremental improvement in various activities",criteria_number:"6.5"},{metric_number:"7.1.1",metric_description:"Measures initiated by the Institution for the promotion of gender equity during the last five years",criteria_number:"7.1"},{metric_number:"7.1.8",metric_description:"Average percentage of expenditure for infrastructure augmentation excluding salary during the last five years",criteria_number:"7.1"},{metric_number:"7.1.9",metric_description:"Sensitization of students and employees of the Institution to the constitutional obligations: values, rights, duties and responsibilities of citizens",criteria_number:"7.1"},{metric_number:"7.1.10",metric_description:"The Institution has a prescribed code of conduct for students, teachers, administrators and other staff and conducts periodic programmes in this regard",criteria_number:"7.1"},{metric_number:"7.1.11",metric_description:"Institution celebrates/organizes national and international commemorative days, events and festivals",criteria_number:"7.1"},{metric_number:"7.2.1",metric_description:"Describe two best practices successfully implemented by the Institution as per NAAC format provided in the Manual",criteria_number:"7.2"},{metric_number:"7.3.1",metric_description:"Portray the performance of the Institution in one area distinctive to its priority and thrust within a maximum of 500 words",criteria_number:"7.3"}],n=e=>i.find(t=>t.metric_number===e)},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6487:()=>{},7910:e=>{"use strict";e.exports=require("stream")},8335:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,580,928,728],()=>r(634));module.exports=i})();