"use strict";(()=>{var t={};t.id=967,t.ids=[967],t.modules={846:t=>{t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:t=>{t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:t=>{t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},9294:t=>{t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9606:(t,e,s)=>{s.r(e),s.d(e,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>b,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>_});var i={};s.r(i),s.d(i,{GET:()=>m});var a=s(6559),n=s(8088),r=s(7719),o=s(2190),u=s(2178),l=s(453),d=s(2869);async function m(t){try{let{searchParams:e}=new URL(t.url);switch(e.get("type")||"overview"){case"overview":let s=u.a.getSubmissionStatistics(),i={summary:{totalInstitutions:l.iv.length,totalMetrics:d.Cb.length,totalSubmissions:s.totalSubmissions,passedValidations:s.passedValidations,failedValidations:s.failedValidations,submittedToQMB:s.submittedToQMB,successRate:s.totalSubmissions>0?Math.round(s.passedValidations/s.totalSubmissions*100):0},trends:{recentSubmissions:s.recentSubmissions.map(t=>({id:t.id,timestamp:t.timestamp,instituteCode:t.institute_code,metricNumber:t.metric_number,status:t.validation_status,submittedToQMB:t.submitted_to_qmb}))},topInstitutes:Object.entries(s.byInstitute).sort(([,t],[,e])=>e-t).slice(0,10).map(([t,e])=>{let s=l.iv.find(e=>e.institute_code===t);return{code:t,name:s?.institute_short_name||"Unknown",fullName:s?.institute_name||"Unknown",submissions:e}}),topMetrics:Object.entries(s.byMetric).sort(([,t],[,e])=>e-t).slice(0,10).map(([t,e])=>{let s=d.Cb.find(e=>e.metric_number===t);return{number:t,description:s?.metric_description||"Unknown",submissions:e}})};return o.NextResponse.json({success:!0,data:i,message:"Overview statistics retrieved successfully"});case"institute":return function(t){if(!t)return o.NextResponse.json({success:!1,error:"Institute code is required"},{status:400});let e=l.iv.find(e=>e.institute_code===t);if(!e)return o.NextResponse.json({success:!1,error:"Invalid institute code"},{status:404});let s=u.a.getSubmissionsByInstitute(t),i={institute:{code:e.institute_code,shortName:e.institute_short_name,fullName:e.institute_name,faculty:e.faculty,city:e.city},statistics:{totalSubmissions:s.length,passedValidations:s.filter(t=>"passed"===t.validation_status).length,failedValidations:s.filter(t=>"failed"===t.validation_status).length,submittedToQMB:s.filter(t=>t.submitted_to_qmb).length,successRate:s.length>0?Math.round(s.filter(t=>"passed"===t.validation_status).length/s.length*100):0},metricBreakdown:s.reduce((t,e)=>{let s=d.Cb.find(t=>t.metric_number===e.metric_number);return t[e.metric_number]||(t[e.metric_number]={metricNumber:e.metric_number,metricDescription:s?.metric_description||"Unknown",totalSubmissions:0,passed:0,failed:0,submitted:0}),t[e.metric_number].totalSubmissions++,"passed"===e.validation_status&&t[e.metric_number].passed++,"failed"===e.validation_status&&t[e.metric_number].failed++,e.submitted_to_qmb&&t[e.metric_number].submitted++,t},{}),recentActivity:s.sort((t,e)=>new Date(e.timestamp).getTime()-new Date(t.timestamp).getTime()).slice(0,20).map(t=>({id:t.id,timestamp:t.timestamp,metricNumber:t.metric_number,fileName:t.original_file_name,status:t.validation_status,errorCount:t.error_count,warningCount:t.warning_count,submittedToQMB:t.submitted_to_qmb}))};return o.NextResponse.json({success:!0,data:i,message:`Statistics for ${e.institute_short_name} retrieved successfully`})}(e.get("code"));case"metric":return function(t){if(!t)return o.NextResponse.json({success:!1,error:"Metric number is required"},{status:400});let e=d.Cb.find(e=>e.metric_number===t);if(!e)return o.NextResponse.json({success:!1,error:"Invalid metric number"},{status:404});let s=u.a.getSubmissionsByMetric(t),i={metric:{number:e.metric_number,description:e.metric_description,criteria:e.criteria_number},statistics:{totalSubmissions:s.length,passedValidations:s.filter(t=>"passed"===t.validation_status).length,failedValidations:s.filter(t=>"failed"===t.validation_status).length,submittedToQMB:s.filter(t=>t.submitted_to_qmb).length,successRate:s.length>0?Math.round(s.filter(t=>"passed"===t.validation_status).length/s.length*100):0},instituteBreakdown:s.reduce((t,e)=>{let s=l.iv.find(t=>t.institute_code===e.institute_code);return t[e.institute_code]||(t[e.institute_code]={instituteCode:e.institute_code,instituteName:s?.institute_short_name||"Unknown",totalSubmissions:0,passed:0,failed:0,submitted:0}),t[e.institute_code].totalSubmissions++,"passed"===e.validation_status&&t[e.institute_code].passed++,"failed"===e.validation_status&&t[e.institute_code].failed++,e.submitted_to_qmb&&t[e.institute_code].submitted++,t},{}),commonErrors:s.filter(t=>t.validation_result?.errors).flatMap(t=>t.validation_result.errors).reduce((t,e)=>{let s=`${e.rule}: ${e.error}`;return t[s]=(t[s]||0)+1,t},{}),recentActivity:s.sort((t,e)=>new Date(e.timestamp).getTime()-new Date(t.timestamp).getTime()).slice(0,20).map(t=>({id:t.id,timestamp:t.timestamp,instituteCode:t.institute_code,fileName:t.original_file_name,status:t.validation_status,errorCount:t.error_count,warningCount:t.warning_count,submittedToQMB:t.submitted_to_qmb}))};return o.NextResponse.json({success:!0,data:i,message:`Statistics for metric ${t} retrieved successfully`})}(e.get("number"));case"detailed":return function(){let t=u.a.getSubmissionStatistics(),e=u.a.getSubmissions(),s=new Date,i=e.filter(t=>s.getTime()-new Date(t.timestamp).getTime()<864e5),a=e.filter(t=>s.getTime()-new Date(t.timestamp).getTime()<6048e5),n=e.filter(t=>s.getTime()-new Date(t.timestamp).getTime()<2592e6),r={overview:{totalInstitutions:l.iv.length,activeInstitutions:Object.keys(t.byInstitute).length,totalMetrics:d.Cb.length,activeMetrics:Object.keys(t.byMetric).length,totalSubmissions:t.totalSubmissions,successRate:t.totalSubmissions>0?Math.round(t.passedValidations/t.totalSubmissions*100):0},timeBasedStats:{last24Hours:{total:i.length,passed:i.filter(t=>"passed"===t.validation_status).length,failed:i.filter(t=>"failed"===t.validation_status).length},last7Days:{total:a.length,passed:a.filter(t=>"passed"===t.validation_status).length,failed:a.filter(t=>"failed"===t.validation_status).length},last30Days:{total:n.length,passed:n.filter(t=>"passed"===t.validation_status).length,failed:n.filter(t=>"failed"===t.validation_status).length}},institutionStats:t.byInstitute,metricStats:t.byMetric,validationErrors:e.filter(t=>t.validation_result?.errors).flatMap(t=>t.validation_result.errors).reduce((t,e)=>(t[e.rule]=(t[e.rule]||0)+1,t),{}),submissionTrends:function(t){let e=[],s=new Date;for(let i=29;i>=0;i--){let a=new Date(s);a.setDate(a.getDate()-i);let n=a.toISOString().split("T")[0],r=t.filter(t=>new Date(t.timestamp).toISOString().split("T")[0]===n);e.push({date:n,total:r.length,passed:r.filter(t=>"passed"===t.validation_status).length,failed:r.filter(t=>"failed"===t.validation_status).length})}return e}(e)};return o.NextResponse.json({success:!0,data:r,message:"Detailed statistics retrieved successfully"})}();default:return o.NextResponse.json({success:!1,error:"Invalid stats type"},{status:400})}}catch(t){return console.error("Stats API error:",t),o.NextResponse.json({success:!1,error:t instanceof Error?t.message:"Internal server error",message:"Failed to retrieve statistics"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/stats/route",pathname:"/api/stats",filename:"route",bundlePath:"app/api/stats/route"},resolvedPagePath:"/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/api/stats/route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:p,workUnitAsyncStorage:_,serverHooks:b}=c;function f(){return(0,r.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:_})}}};var e=require("../../../webpack-runtime.js");e.C(t);var s=t=>e(e.s=t),i=e.X(0,[447,580,437,662],()=>s(9606));module.exports=i})();