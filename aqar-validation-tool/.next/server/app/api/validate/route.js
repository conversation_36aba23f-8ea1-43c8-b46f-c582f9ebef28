"use strict";(()=>{var e={};e.id=996,e.ids=[996],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6660:(e,i,t)=>{t.r(i),t.d(i,{patchFetch:()=>w,routeModule:()=>f,serverHooks:()=>b,workAsyncStorage:()=>V,workUnitAsyncStorage:()=>g});var r={};t.r(r),t.d(r,{GET:()=>v,POST:()=>h});var a=t(6559),s=t(8088),n=t(7719),l=t(2190),o=t(1670),d=t(453);class u{constructor(){this.validators={year_range:this.validateYearRange.bind(this),exact_match:this.validateExactMatch.bind(this),contains_text:this.validateContainsText.bind(this),faculty_choice:this.validateFacultyChoice.bind(this),institute_code_choice:this.validateInstituteCodeChoice.bind(this),institute_shortname_choice:this.validateInstituteShortNameChoice.bind(this),institute_name_choice:this.validateInstituteNameChoice.bind(this),fixed_digits:this.validateFixedDigits.bind(this),required_field:this.validateRequiredField.bind(this),word_limit:this.validateWordLimit.bind(this),pdf_file:this.validatePdfFile.bind(this),yes_no_choice:this.validateYesNoChoice.bind(this),binary_choice:this.validateBinaryChoice.bind(this),text_length:this.validateTextLength.bind(this),year:this.validateYear.bind(this),numeric:this.validateNumeric.bind(this),currency:this.validateCurrency.bind(this),url_pattern:this.validateUrlPattern.bind(this),image_file:this.validateImageFile.bind(this),date:this.validateDate.bind(this),reference_lookup:this.validateReferenceLookup.bind(this)}}async validateCSV(e,i,t){let r=new Date,a=(0,o.id)(i),s=[],n=this.validateHeaders(e.headers,a);s.push(...n);for(let i=0;i<e.rows.length;i++){let t=e.rows[i],r=this.validateRow(t,e.headers,a,i+2);s.push(...r)}let l=this.generateValidationSummary(s,e.rows.length);return{isValid:0===s.filter(e=>"error"===e.severity).length,errorCount:s.filter(e=>"error"===e.severity).length,warningCount:s.filter(e=>"warning"===e.severity).length,errors:s,summary:l,processedRows:e.rows.length,timestamp:r}}validateHeaders(e,i){let t=[];for(let r of i.filter(e=>e.is_required).map(e=>e.column_name))e.includes(r)||t.push({row:1,column:r,value:"",error:`Required column '${r}' is missing from CSV headers`,rule:"required_field",severity:"error"});return t}validateRow(e,i,t,r){let a=[];for(let s of t){let t=i.indexOf(s.column_name);if(-1===t)continue;let n=e[t]||"",l=this.validators[s.validation_type];if(l){let i=l(n,s.validation_params,r,[e]);i.isValid||a.push({row:r,column:s.column_name,value:n,error:i.error||s.error_message,rule:s.validation_type,severity:s.is_required?"error":"warning"})}}return a}generateValidationSummary(e,i){let t=new Set(e.filter(e=>"error"===e.severity).map(e=>e.row)).size,r=new Set(e.filter(e=>"warning"===e.severity).map(e=>e.row)).size,a={};return e.forEach(e=>{let i=`${e.rule}: ${e.error}`;a[i]=(a[i]||0)+1}),{totalRows:i,validRows:i-t,errorRows:t,warningRows:r,criticalErrors:e.filter(e=>"error"===e.severity).slice(0,10),commonErrors:a}}validateYearRange(e,i){if(!e.trim())return{isValid:!0};let[t,r]=i.split("-").map(e=>parseInt(e)),a=parseInt(e);return isNaN(a)?{isValid:!1,error:"Invalid year format"}:a<t||a>r?{isValid:!1,error:`Year must be between ${t} and ${r}`}:{isValid:!0}}validateExactMatch(e,i){if(!e.trim())return{isValid:!0};let t=i.split(",").map(e=>e.trim());return t.includes(e.trim())?{isValid:!0}:{isValid:!1,error:`Value must be one of: ${t.join(", ")}`}}validateContainsText(e,i){if(!e.trim())return{isValid:!0};let t=i.split(",").map(e=>e.trim().toLowerCase()),r=e.toLowerCase();return t.some(e=>r.includes(e))?{isValid:!0}:{isValid:!1,error:`Text must contain one of: ${t.join(", ")}`}}validateFacultyChoice(e,i){if(!e.trim())return{isValid:!0};let t=i.split(",").map(e=>e.trim());return t.includes(e.trim())?{isValid:!0}:{isValid:!1,error:`Invalid faculty. Must be one of: ${t.join(", ")}`}}validateInstituteCodeChoice(e){return e.trim()?d.iv.map(e=>e.institute_code).includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Invalid institute code"}:{isValid:!0}}validateInstituteShortNameChoice(e){return e.trim()?d.iv.map(e=>e.institute_short_name).includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Invalid institute short name"}:{isValid:!0}}validateInstituteNameChoice(e){return e.trim()?d.iv.map(e=>e.institute_name).includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Invalid institute name"}:{isValid:!0}}validateFixedDigits(e,i){if(!e.trim())return{isValid:!0};let t=parseInt(i);return/^\d+$/.test(e)&&e.length===t?{isValid:!0}:{isValid:!1,error:`Must be exactly ${t} digits`}}validateRequiredField(e){return e&&e.trim()?{isValid:!0}:{isValid:!1,error:"This field is required"}}validateWordLimit(e,i){if(!e.trim())return{isValid:!0};let t=parseInt(i),r=e.trim().split(/\s+/).length;return r>t?{isValid:!1,error:`Exceeds word limit of ${t} words (current: ${r})`}:{isValid:!0}}validatePdfFile(e){return e.trim()?e.toLowerCase().endsWith(".pdf")?{isValid:!0}:{isValid:!1,error:"File must be a PDF"}:{isValid:!0}}validateYesNoChoice(e){return e.trim()?["Yes","No","YES","NO","yes","no"].includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Value must be Yes or No"}:{isValid:!0}}validateBinaryChoice(e,i){if(!e.trim())return{isValid:!0};let[t,r]=i.split(",").map(e=>e.trim());return[t,r].includes(e.trim())?{isValid:!0}:{isValid:!1,error:`Value must be ${t} or ${r}`}}validateTextLength(e,i){if(!e.trim())return{isValid:!0};let[t,r]=i.split(","),a=parseInt(t.split(":")[1]),s=parseInt(r.split(":")[1]);return e.length<a||e.length>s?{isValid:!1,error:`Text length must be between ${a} and ${s} characters`}:{isValid:!0}}validateYear(e){if(!e.trim())return{isValid:!0};let i=parseInt(e),t=new Date().getFullYear();return isNaN(i)||i<1900||i>t+10?{isValid:!1,error:`Invalid year. Must be between 1900 and ${t+10}`}:{isValid:!0}}validateNumeric(e,i){if(!e.trim())return{isValid:!0};let t=parseFloat(e);if(isNaN(t))return{isValid:!1,error:"Value must be a number"};if(i)for(let e of i.split(",")){let[i,r]=e.split(":"),a=parseFloat(r);if("min"===i&&t<a)return{isValid:!1,error:`Value must be at least ${a}`};if("max"===i&&t>a)return{isValid:!1,error:`Value must not exceed ${a}`}}return{isValid:!0}}validateCurrency(e,i){if(!e.trim())return{isValid:!0};let t=e.replace(/[₹$,\s]/g,"");return isNaN(parseFloat(t))?{isValid:!1,error:"Invalid currency format"}:this.validateNumeric(t,i)}validateUrlPattern(e){if(!e.trim())return{isValid:!0};try{return new URL(e),{isValid:!0}}catch{return{isValid:!1,error:"Invalid URL format"}}}validateImageFile(e){return e.trim()?[".jpg",".jpeg",".png",".gif",".bmp",".webp"].some(i=>e.toLowerCase().endsWith(i))?{isValid:!0}:{isValid:!1,error:"File must be an image (jpg, jpeg, png, gif, bmp, webp)"}:{isValid:!0}}validateDate(e,i){if(!e.trim())return{isValid:!0};let t=i.split(","),r="DD/MM/YYYY",a="";for(let e of t){let[i,t]=e.split(":");"format"===i&&(r=t),"range"===i&&(a=t)}let s=e.match(/^(\d{2})\/(\d{2})\/(\d{4})$/);if(!s)return{isValid:!1,error:`Date must be in ${r} format`};let[,n,l,o]=s,d=new Date(parseInt(o),parseInt(l)-1,parseInt(n));if(d.getDate()!==parseInt(n)||d.getMonth()!==parseInt(l)-1||d.getFullYear()!==parseInt(o))return{isValid:!1,error:"Invalid date"};if(a){let[e,i]=a.split("-"),[t,r,s]=e.split("/"),[n,l,o]=i.split("/"),u=new Date(parseInt(s),parseInt(r)-1,parseInt(t)),m=new Date(parseInt(o),parseInt(l)-1,parseInt(n));if(d<u||d>m)return{isValid:!1,error:`Date must be between ${e} and ${i}`}}return{isValid:!0}}validateReferenceLookup(e,i){return!e.trim(),{isValid:!0}}}var m=t(4537),c=t(2178),p=t(2869);async function h(e){try{let i=await e.formData(),t=i.get("file"),r=i.get("instituteCode"),a=i.get("metricNumber");if(!t||!r||!a)return l.NextResponse.json({success:!1,error:"Missing required parameters: file, instituteCode, or metricNumber"},{status:400});let s=(0,d.bK)(r),n=(0,p.pF)(a);if(!s)return l.NextResponse.json({success:!1,error:`Invalid institute code: ${r}`},{status:400});if(!n)return l.NextResponse.json({success:!1,error:`Invalid metric number: ${a}`},{status:400});let o=m.N.validateCSVFile(t);if(!o.isValid)return l.NextResponse.json({success:!1,error:o.error},{status:400});let h=await m.N.parseCSVFile(t),v=new u,f=await v.validateCSV(h,a,r),V=null;(f.isValid||0===f.errorCount)&&(V=await c.a.uploadFile(t,r,a));let g=c.a.saveSubmission(r,s.institute_name,a,f,V?.filePath||"",t.name);return l.NextResponse.json({success:!0,data:{validationResult:f,submission:g,uploadResult:V,csvData:{fileName:h.fileName,fileSize:h.fileSize,rowCount:h.rowCount,columnCount:h.headers.length}},message:f.isValid?"Validation completed successfully":"Validation completed with errors"})}catch(e){return console.error("Validation API error:",e),l.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Internal server error",message:"Validation failed due to server error"},{status:500})}}async function v(e){try{let i,{searchParams:t}=new URL(e.url),r=t.get("instituteCode"),a=t.get("metricNumber");return i=r&&a?c.a.getSubmissions().filter(e=>e.institute_code===r&&e.metric_number===a):r?c.a.getSubmissionsByInstitute(r):a?c.a.getSubmissionsByMetric(a):c.a.getSubmissions(),l.NextResponse.json({success:!0,data:i,message:`Retrieved ${i.length} submissions`})}catch(e){return console.error("Get submissions API error:",e),l.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Internal server error",message:"Failed to retrieve submissions"},{status:500})}}let f=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/validate/route",pathname:"/api/validate",filename:"route",bundlePath:"app/api/validate/route"},resolvedPagePath:"/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/api/validate/route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:V,workUnitAsyncStorage:g,serverHooks:b}=f;function w(){return(0,n.patchFetch)({workAsyncStorage:V,workUnitAsyncStorage:g})}},7910:e=>{e.exports=require("stream")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var i=require("../../../webpack-runtime.js");i.C(e);var t=e=>i(i.s=e),r=i.X(0,[447,580,437,928,662,728],()=>t(6660));module.exports=r})();