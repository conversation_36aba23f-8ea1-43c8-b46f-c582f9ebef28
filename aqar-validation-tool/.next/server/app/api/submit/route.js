"use strict";(()=>{var e={};e.id=134,e.ids=[134],e.modules={846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4547:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>v,routeModule:()=>b,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var r={};s.r(r),s.d(r,{GET:()=>l,POST:()=>c});var i=s(6559),o=s(8088),n=s(7719),a=s(2190),u=s(2178),d=s(453),m=s(2869);async function c(e){try{let{submissionId:t,userInfo:s}=await e.json();if(!t)return a.NextResponse.json({success:!1,error:"Missing required parameter: submissionId"},{status:400});let r=u.a.getSubmissions().find(e=>e.id===t);if(!r)return a.NextResponse.json({success:!1,error:`Submission not found: ${t}`},{status:404});if("passed"!==r.validation_status)return a.NextResponse.json({success:!1,error:"Cannot submit file with validation errors. Please fix errors and re-validate."},{status:400});if(r.submitted_to_qmb)return a.NextResponse.json({success:!1,error:"This submission has already been submitted to QMB"},{status:400});if(!u.a.markAsSubmittedToQMB(t))return a.NextResponse.json({success:!1,error:"Failed to update submission status"},{status:500});return await p(r,s),a.NextResponse.json({success:!0,data:{submissionId:t,submittedAt:new Date().toISOString(),status:"submitted_to_qmb"},message:"File successfully submitted to QMB for review"})}catch(e){return console.error("Submit API error:",e),a.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Internal server error",message:"Submission to QMB failed"},{status:500})}}async function l(e){try{let{searchParams:t}=new URL(e.url),s=t.get("instituteCode"),r=t.get("metricNumber"),i=t.get("status"),o=u.a.getSubmissions();return s&&(o=o.filter(e=>e.institute_code===s)),r&&(o=o.filter(e=>e.metric_number===r)),"submitted"===i?o=o.filter(e=>e.submitted_to_qmb):"pending"===i&&(o=o.filter(e=>!e.submitted_to_qmb&&"passed"===e.validation_status)),o.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()),a.NextResponse.json({success:!0,data:o,message:`Retrieved ${o.length} submissions`})}catch(e){return console.error("Get submissions API error:",e),a.NextResponse.json({success:!1,error:e instanceof Error?e.message:"Internal server error",message:"Failed to retrieve submissions"},{status:500})}}async function p(e,t){await new Promise(e=>setTimeout(e,1e3));let s=(0,d.bK)(e.institute_code),r=(0,m.pF)(e.metric_number);console.log("QMB Submission Details:",{submissionId:e.id,instituteCode:e.institute_code,instituteName:s?.institute_name,metricNumber:e.metric_number,metricDescription:r?.metric_description,fileName:e.file_name,submittedAt:new Date().toISOString(),userInfo:t||"Not provided",validationSummary:{totalRows:e.validation_result?.processedRows,errorCount:e.error_count,warningCount:e.warning_count}})}let b=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/submit/route",pathname:"/api/submit",filename:"route",bundlePath:"app/api/submit/route"},resolvedPagePath:"/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/api/submit/route.ts",nextConfigOutput:"standalone",userland:r}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:x}=b;function v(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},4870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},9294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,580,437,662],()=>s(4547));module.exports=r})();