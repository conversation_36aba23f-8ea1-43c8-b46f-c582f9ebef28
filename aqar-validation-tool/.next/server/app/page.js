(()=>{var e={};e.id=974,e.ids=[974],e.modules={184:()=>{},440:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>n});var r=i(1658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},554:(e,t)=>{"use strict";function i(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return i}})},660:(e,t)=>{"use strict";function i(e){let t=5381;for(let i=0;i<e.length;i++)t=(t<<5)+t+e.charCodeAt(i)|0;return t>>>0}function r(e){return i(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{djb2Hash:function(){return i},hexHash:function(){return r}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>r});let r=(0,i(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/page.tsx","default")},1658:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{fillMetadataSegment:function(){return m},normalizeMetadataPageToRoute:function(){return h},normalizeMetadataRoute:function(){return p}});let r=i(8304),n=function(e){return e&&e.__esModule?e:{default:e}}(i(8671)),a=i(6341),s=i(4396),o=i(660),l=i(4722),u=i(2958),c=i(5499);function d(e){let t=n.default.dirname(e);if(e.endsWith("/sitemap"))return"";let i="";return t.split("/").some(e=>(0,c.isGroupSegment)(e)||(0,c.isParallelRouteSegment)(e))&&(i=(0,o.djb2Hash)(t).toString(36).slice(0,6)),i}function m(e,t,i){let r=(0,l.normalizeAppPath)(e),o=(0,s.getNamedRouteRegex)(r,{prefixRouteKeys:!1}),c=(0,a.interpolateDynamicPath)(r,t,o),{name:m,ext:p}=n.default.parse(i),h=d(n.default.posix.join(e,m)),f=h?`-${h}`:"";return(0,u.normalizePathSep)(n.default.join(c,`${m}${f}${p}`))}function p(e){if(!(0,r.isMetadataPage)(e))return e;let t=e,i="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":i=d(e),!t.endsWith("/route")){let{dir:e,name:r,ext:a}=n.default.parse(t);t=n.default.posix.join(e,`${r}${i?`-${i}`:""}${a}`,"route")}return t}function h(e,t){let i=e.endsWith("/route"),r=i?e.slice(0,-6):e,n=r.endsWith("/sitemap")?".xml":"";return(t?`${r}/[__metadata_id__]`:`${r}${n}`)+(i?"/route":"")}},2011:(e,t,i)=>{Promise.resolve().then(i.bind(i,4595))},2437:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return n}});let r=i(5362);function n(e,t){let i=[],n=(0,r.pathToRegexp)(e,i,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,r.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(n.source),n.flags):n,i);return(e,r)=>{if("string"!=typeof e)return!1;let n=a(e);if(!n)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of i)"number"==typeof e.name&&delete n.params[e.name];return{...r,...n.params}}}},2785:(e,t)=>{"use strict";function i(e){let t={};for(let[i,r]of e.entries()){let e=t[i];void 0===e?t[i]=r:Array.isArray(e)?e.push(r):t[i]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function n(e){let t=new URLSearchParams;for(let[i,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(i,r(e));else t.set(i,r(n));return t}function a(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];for(let t of i){for(let i of t.keys())e.delete(i);for(let[i,r]of t.entries())e.append(i,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return n}})},2958:(e,t)=>{"use strict";function i(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return i}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return n}});let i=/[|\\{}()[\]^$+*?.-]/,r=/[|\\{}()[\]^$+*?.-]/g;function n(e){return i.test(e)?e.replace(r,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3736:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return n}}),i(4827);let r=i(2785);function n(e,t,i){void 0===i&&(i=!0);let n=new URL("http://n"),a=t?new URL(t,n):e.startsWith(".")?new URL("http://n"):n,{pathname:s,searchParams:o,search:l,hash:u,href:c,origin:d}=new URL(e,a);if(d!==n.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:s,query:i?(0,r.searchParamsToUrlQuery)(o):void 0,search:l,hash:u,href:c.slice(d.length)}}},3818:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>s.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>m,tree:()=>u});var r=i(5239),n=i(8088),a=i(8170),s=i.n(a),o=i(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);i.d(t,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,1204)),"/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,4431)),"/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(i.t.bind(i,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(i.t.bind(i,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Downloads/AI Projects Analysis Download folder/AQAR Validation tool/AQAR Validation tool augment code 19 june/aqar-validation-tool/src/app/page.tsx"],d={require:i,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},3873:e=>{"use strict";e.exports=require("path")},4396:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getNamedMiddlewareRegex:function(){return f},getNamedRouteRegex:function(){return h},getRouteRegex:function(){return d},parseParameter:function(){return l}});let r=i(6143),n=i(6199),a=i(3293),s=i(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let i=e.startsWith("...");return i&&(e=e.slice(3)),{key:e,repeat:i,optional:t}}function c(e,t,i){let r={},l=1,c=[];for(let d of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),s=d.match(o);if(e&&s&&s[2]){let{key:t,optional:i,repeat:n}=u(s[2]);r[t]={pos:l++,repeat:n,optional:i},c.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(s&&s[2]){let{key:e,repeat:t,optional:n}=u(s[2]);r[e]={pos:l++,repeat:t,optional:n},i&&s[1]&&c.push("/"+(0,a.escapeStringRegexp)(s[1]));let o=t?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";i&&s[1]&&(o=o.substring(1)),c.push(o)}else c.push("/"+(0,a.escapeStringRegexp)(d));t&&s&&s[3]&&c.push((0,a.escapeStringRegexp)(s[3]))}return{parameterizedRoute:c.join(""),groups:r}}function d(e,t){let{includeSuffix:i=!1,includePrefix:r=!1,excludeOptionalTrailingSlash:n=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:s}=c(e,i,r),o=a;return n||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:s}}function m(e){let t,{interceptionMarker:i,getSafeRouteKey:r,segment:n,routeKeys:s,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:c,optional:d,repeat:m}=u(n),p=c.replace(/\W/g,"");o&&(p=""+o+p);let h=!1;(0===p.length||p.length>30)&&(h=!0),isNaN(parseInt(p.slice(0,1)))||(h=!0),h&&(p=r());let f=p in s;o?s[p]=""+o+c:s[p]=c;let _=i?(0,a.escapeStringRegexp)(i):"";return t=f&&l?"\\k<"+p+">":m?"(?<"+p+">.+?)":"(?<"+p+">[^/]+?)",d?"(?:/"+_+t+")?":"/"+_+t}function p(e,t,i,l,u){let c,d=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),p={},h=[];for(let c of(0,s.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),s=c.match(o);if(e&&s&&s[2])h.push(m({getSafeRouteKey:d,interceptionMarker:s[1],segment:s[2],routeKeys:p,keyPrefix:t?r.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(s&&s[2]){l&&s[1]&&h.push("/"+(0,a.escapeStringRegexp)(s[1]));let e=m({getSafeRouteKey:d,segment:s[2],routeKeys:p,keyPrefix:t?r.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&s[1]&&(e=e.substring(1)),h.push(e)}else h.push("/"+(0,a.escapeStringRegexp)(c));i&&s&&s[3]&&h.push((0,a.escapeStringRegexp)(s[3]))}return{namedParameterizedRoute:h.join(""),routeKeys:p}}function h(e,t){var i,r,n;let a=p(e,t.prefixRouteKeys,null!=(i=t.includeSuffix)&&i,null!=(r=t.includePrefix)&&r,null!=(n=t.backreferenceDuplicateKeys)&&n),s=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(s+="(?:/)?"),{...d(e,t),namedRegex:"^"+s+"$",routeKeys:a.routeKeys}}function f(e,t){let{parameterizedRoute:i}=c(e,!1,!1),{catchAll:r=!0}=t;if("/"===i)return{namedRegex:"^/"+(r?".*":"")+"$"};let{namedParameterizedRoute:n}=p(e,!1,!1,!1,!1);return{namedRegex:"^"+n+(r?"(?:(/.*)?)":"")+"$"}}},4430:(e,t,i)=>{"use strict";var r,n;void 0===(n="function"==typeof(r=function e(){var t="undefined"!=typeof self?self:void 0!==t?t:{},r=!t.document&&!!t.postMessage,n=t.IS_PAPA_WORKER||!1,a={},s=0,o={};if(o.parse=function(i,r){var n,l=(r=r||{}).dynamicTyping||!1;if(E(l)&&(r.dynamicTypingFunction=l,l={}),r.dynamicTyping=l,r.transform=!!E(r.transform)&&r.transform,r.worker&&o.WORKERS_SUPPORTED){var u=function(){if(!o.WORKERS_SUPPORTED)return!1;var i,r,n=(i=t.URL||t.webkitURL||null,r=e.toString(),o.BLOB_URL||(o.BLOB_URL=i.createObjectURL(new Blob(["var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; ","(",r,")();"],{type:"text/javascript"})))),l=new t.Worker(n);return l.onmessage=y,l.id=s++,a[l.id]=l,l}();u.userStep=r.step,u.userChunk=r.chunk,u.userComplete=r.complete,u.userError=r.error,r.step=E(r.step),r.chunk=E(r.chunk),r.complete=E(r.complete),r.error=E(r.error),delete r.worker,u.postMessage({input:i,config:r,workerId:u.id});return}var f=null;return i===o.NODE_STREAM_INPUT&&"undefined"==typeof PAPA_BROWSER_CONTEXT?(f=new h(r)).getStream():("string"==typeof i?(i=65279===(n=i).charCodeAt(0)?n.slice(1):n,f=r.download?new c(r):new m(r)):!0===i.readable&&E(i.read)&&E(i.on)?f=new p(r):(t.File&&i instanceof File||i instanceof Object)&&(f=new d(r)),f.stream(i))},o.unparse=function(e,t){var i=!1,r=!0,n=",",a="\r\n",s='"',l=s+s,u=!1,c=null,d=!1;if("object"==typeof t){if("string"!=typeof t.delimiter||o.BAD_DELIMITERS.filter(function(e){return -1!==t.delimiter.indexOf(e)}).length||(n=t.delimiter),("boolean"==typeof t.quotes||"function"==typeof t.quotes||Array.isArray(t.quotes))&&(i=t.quotes),("boolean"==typeof t.skipEmptyLines||"string"==typeof t.skipEmptyLines)&&(u=t.skipEmptyLines),"string"==typeof t.newline&&(a=t.newline),"string"==typeof t.quoteChar&&(s=t.quoteChar),"boolean"==typeof t.header&&(r=t.header),Array.isArray(t.columns)){if(0===t.columns.length)throw Error("Option columns is empty");c=t.columns}void 0!==t.escapeChar&&(l=t.escapeChar+s),t.escapeFormulae instanceof RegExp?d=t.escapeFormulae:"boolean"==typeof t.escapeFormulae&&t.escapeFormulae&&(d=/^[=+\-@\t\r].*$/)}var m=RegExp(_(s),"g");if("string"==typeof e&&(e=JSON.parse(e)),Array.isArray(e)){if(!e.length||Array.isArray(e[0]))return p(null,e,u);else if("object"==typeof e[0])return p(c||Object.keys(e[0]),e,u)}else if("object"==typeof e)return"string"==typeof e.data&&(e.data=JSON.parse(e.data)),Array.isArray(e.data)&&(e.fields||(e.fields=e.meta&&e.meta.fields||c),e.fields||(e.fields=Array.isArray(e.data[0])?e.fields:"object"==typeof e.data[0]?Object.keys(e.data[0]):[]),Array.isArray(e.data[0])||"object"==typeof e.data[0]||(e.data=[e.data])),p(e.fields||[],e.data||[],u);throw Error("Unable to serialize unrecognized input");function p(e,t,i){var s="";"string"==typeof e&&(e=JSON.parse(e)),"string"==typeof t&&(t=JSON.parse(t));var o=Array.isArray(e)&&e.length>0,l=!Array.isArray(t[0]);if(o&&r){for(var u=0;u<e.length;u++)u>0&&(s+=n),s+=h(e[u],u);t.length>0&&(s+=a)}for(var c=0;c<t.length;c++){var d=o?e.length:t[c].length,m=!1,p=o?0===Object.keys(t[c]).length:0===t[c].length;if(i&&!o&&(m="greedy"===i?""===t[c].join("").trim():1===t[c].length&&0===t[c][0].length),"greedy"===i&&o){for(var f=[],_=0;_<d;_++){var g=l?e[_]:_;f.push(t[c][g])}m=""===f.join("").trim()}if(!m){for(var y=0;y<d;y++){y>0&&!p&&(s+=n);var b=o&&l?e[y]:y;s+=h(t[c][b],y)}c<t.length-1&&(!i||d>0&&!p)&&(s+=a)}}return s}function h(e,t){if(null==e)return"";if(e.constructor===Date)return JSON.stringify(e).slice(1,25);var r=!1;d&&"string"==typeof e&&d.test(e)&&(e="'"+e,r=!0);var a=e.toString().replace(m,l);return(r=r||!0===i||"function"==typeof i&&i(e,t)||Array.isArray(i)&&i[t]||function(e,t){for(var i=0;i<t.length;i++)if(e.indexOf(t[i])>-1)return!0;return!1}(a,o.BAD_DELIMITERS)||a.indexOf(n)>-1||" "===a.charAt(0)||" "===a.charAt(a.length-1))?s+a+s:a}},o.RECORD_SEP="\x1e",o.UNIT_SEP="\x1f",o.BYTE_ORDER_MARK="\uFEFF",o.BAD_DELIMITERS=["\r","\n",'"',o.BYTE_ORDER_MARK],o.WORKERS_SUPPORTED=!r&&!!t.Worker,o.NODE_STREAM_INPUT=1,o.LocalChunkSize=0xa00000,o.RemoteChunkSize=5242880,o.DefaultDelimiter=",",o.Parser=g,o.ParserHandle=f,o.NetworkStreamer=c,o.FileStreamer=d,o.StringStreamer=m,o.ReadableStreamStreamer=p,"undefined"==typeof PAPA_BROWSER_CONTEXT&&(o.DuplexStreamStreamer=h),t.jQuery){var l=t.jQuery;l.fn.parse=function(e){var i=e.config||{},r=[];return this.each(function(e){if(!("INPUT"===l(this).prop("tagName").toUpperCase()&&"file"===l(this).attr("type").toLowerCase()&&t.FileReader)||!this.files||0===this.files.length)return!0;for(var n=0;n<this.files.length;n++)r.push({file:this.files[n],inputElem:this,instanceConfig:l.extend({},i)})}),n(),this;function n(){if(0===r.length){E(e.complete)&&e.complete();return}var t=r[0];if(E(e.before)){var i,n,s,u,c=e.before(t.file,t.inputElem);if("object"==typeof c)if("abort"===c.action){return void(i="AbortError",n=t.file,s=t.inputElem,u=c.reason,E(e.error)&&e.error({name:i},n,s,u))}else{if("skip"===c.action)return void a();"object"==typeof c.config&&(t.instanceConfig=l.extend(t.instanceConfig,c.config))}else if("skip"===c)return void a()}var d=t.instanceConfig.complete;t.instanceConfig.complete=function(e){E(d)&&d(e,t.file,t.inputElem),a()},o.parse(t.file,t.instanceConfig)}function a(){r.splice(0,1),n()}}}function u(e){this._handle=null,this._finished=!1,this._completed=!1,this._halted=!1,this._input=null,this._baseIndex=0,this._partialLine="",this._rowCount=0,this._start=0,this._nextChunk=null,this.isFirstChunk=!0,this._completeResults={data:[],errors:[],meta:{}},(function(e){var t=x(e);t.chunkSize=parseInt(t.chunkSize),e.step||e.chunk||(t.chunkSize=null),this._handle=new f(t),this._handle.streamer=this,this._config=t}).call(this,e),this.parseChunk=function(e,i){let r=parseInt(this._config.skipFirstNLines)||0;if(this.isFirstChunk&&r>0){let t=this._config.newline;if(!t){let i=this._config.quoteChar||'"';t=this._handle.guessLineEndings(e,i)}e=[...e.split(t).slice(r)].join(t)}if(this.isFirstChunk&&E(this._config.beforeFirstChunk)){var a=this._config.beforeFirstChunk(e);void 0!==a&&(e=a)}this.isFirstChunk=!1,this._halted=!1;var s=this._partialLine+e;this._partialLine="";var l=this._handle.parse(s,this._baseIndex,!this._finished);if(this._handle.paused()||this._handle.aborted()){this._halted=!0;return}var u=l.meta.cursor;this._finished||(this._partialLine=s.substring(u-this._baseIndex),this._baseIndex=u),l&&l.data&&(this._rowCount+=l.data.length);var c=this._finished||this._config.preview&&this._rowCount>=this._config.preview;if(n)t.postMessage({results:l,workerId:o.WORKER_ID,finished:c});else if(E(this._config.chunk)&&!i){if(this._config.chunk(l,this._handle),this._handle.paused()||this._handle.aborted()){this._halted=!0;return}l=void 0,this._completeResults=void 0}return this._config.step||this._config.chunk||(this._completeResults.data=this._completeResults.data.concat(l.data),this._completeResults.errors=this._completeResults.errors.concat(l.errors),this._completeResults.meta=l.meta),!this._completed&&c&&E(this._config.complete)&&(!l||!l.meta.aborted)&&(this._config.complete(this._completeResults,this._input),this._completed=!0),c||l&&l.meta.paused||this._nextChunk(),l},this._sendError=function(e){E(this._config.error)?this._config.error(e):n&&this._config.error&&t.postMessage({workerId:o.WORKER_ID,error:e,finished:!1})}}function c(e){var t;(e=e||{}).chunkSize||(e.chunkSize=o.RemoteChunkSize),u.call(this,e),r?this._nextChunk=function(){this._readChunk(),this._chunkLoaded()}:this._nextChunk=function(){this._readChunk()},this.stream=function(e){this._input=e,this._nextChunk()},this._readChunk=function(){if(this._finished)return void this._chunkLoaded();if(t=new XMLHttpRequest,this._config.withCredentials&&(t.withCredentials=this._config.withCredentials),r||(t.onload=S(this._chunkLoaded,this),t.onerror=S(this._chunkError,this)),t.open(this._config.downloadRequestBody?"POST":"GET",this._input,!r),this._config.downloadRequestHeaders){var e=this._config.downloadRequestHeaders;for(var i in e)t.setRequestHeader(i,e[i])}if(this._config.chunkSize){var n=this._start+this._config.chunkSize-1;t.setRequestHeader("Range","bytes="+this._start+"-"+n)}try{t.send(this._config.downloadRequestBody)}catch(e){this._chunkError(e.message)}r&&0===t.status&&this._chunkError()},this._chunkLoaded=function(){if(4===t.readyState){var e;if(t.status<200||t.status>=400)return void this._chunkError();this._start+=this._config.chunkSize?this._config.chunkSize:t.responseText.length,this._finished=!this._config.chunkSize||this._start>=(null===(e=t.getResponseHeader("Content-Range"))?-1:parseInt(e.substring(e.lastIndexOf("/")+1))),this.parseChunk(t.responseText)}},this._chunkError=function(e){var i=t.statusText||e;this._sendError(Error(i))}}function d(e){(e=e||{}).chunkSize||(e.chunkSize=o.LocalChunkSize),u.call(this,e);var t,i,r="undefined"!=typeof FileReader;this.stream=function(e){this._input=e,i=e.slice||e.webkitSlice||e.mozSlice,r?((t=new FileReader).onload=S(this._chunkLoaded,this),t.onerror=S(this._chunkError,this)):t=new FileReaderSync,this._nextChunk()},this._nextChunk=function(){this._finished||this._config.preview&&!(this._rowCount<this._config.preview)||this._readChunk()},this._readChunk=function(){var e=this._input;if(this._config.chunkSize){var n=Math.min(this._start+this._config.chunkSize,this._input.size);e=i.call(e,this._start,n)}var a=t.readAsText(e,this._config.encoding);r||this._chunkLoaded({target:{result:a}})},this._chunkLoaded=function(e){this._start+=this._config.chunkSize,this._finished=!this._config.chunkSize||this._start>=this._input.size,this.parseChunk(e.target.result)},this._chunkError=function(){this._sendError(t.error)}}function m(e){var t;e=e||{},u.call(this,e),this.stream=function(e){return t=e,this._nextChunk()},this._nextChunk=function(){if(!this._finished){var e,i=this._config.chunkSize;return i?(e=t.substring(0,i),t=t.substring(i)):(e=t,t=""),this._finished=!t,this.parseChunk(e)}}}function p(e){e=e||{},u.call(this,e);var t=[],i=!0,r=!1;this.pause=function(){u.prototype.pause.apply(this,arguments),this._input.pause()},this.resume=function(){u.prototype.resume.apply(this,arguments),this._input.resume()},this.stream=function(e){this._input=e,this._input.on("data",this._streamData),this._input.on("end",this._streamEnd),this._input.on("error",this._streamError)},this._checkIsFinished=function(){r&&1===t.length&&(this._finished=!0)},this._nextChunk=function(){this._checkIsFinished(),t.length?this.parseChunk(t.shift()):i=!0},this._streamData=S(function(e){try{t.push("string"==typeof e?e:e.toString(this._config.encoding)),i&&(i=!1,this._checkIsFinished(),this.parseChunk(t.shift()))}catch(e){this._streamError(e)}},this),this._streamError=S(function(e){this._streamCleanUp(),this._sendError(e)},this),this._streamEnd=S(function(){this._streamCleanUp(),r=!0,this._streamData("")},this),this._streamCleanUp=S(function(){this._input.removeListener("data",this._streamData),this._input.removeListener("end",this._streamEnd),this._input.removeListener("error",this._streamError)},this)}function h(e){var t=i(7910).Duplex,r=x(e),n=!0,a=!1,s=[],o=null;this._onCsvData=function(e){var t=e.data;o.push(t)||this._handle.paused()||this._handle.pause()},this._onCsvComplete=function(){o.push(null)},r.step=S(this._onCsvData,this),r.complete=S(this._onCsvComplete,this),u.call(this,r),this._nextChunk=function(){a&&1===s.length&&(this._finished=!0),s.length?s.shift()():n=!0},this._addToParseQueue=function(e,t){s.push(S(function(){if(this.parseChunk("string"==typeof e?e:e.toString(r.encoding)),E(t))return t()},this)),n&&(n=!1,this._nextChunk())},this._onRead=function(){this._handle.paused()&&this._handle.resume()},this._onWrite=function(e,t,i){this._addToParseQueue(e,i)},this._onWriteComplete=function(){a=!0,this._addToParseQueue("")},this.getStream=function(){return o},(o=new t({readableObjectMode:!0,decodeStrings:!1,read:S(this._onRead,this),write:S(this._onWrite,this)})).once("finish",S(this._onWriteComplete,this))}function f(e){var t,i,r,n=/^\s*-?(\d+\.?|\.\d+|\d+\.\d+)([eE][-+]?\d+)?\s*$/,a=/^((\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z)))$/,s=this,l=0,u=0,c=!1,d=!1,m=[],p={data:[],errors:[],meta:{}};if(E(e.step)){var h=e.step;e.step=function(t){if(p=t,b())y();else{if(y(),0===p.data.length)return;l+=t.data.length,e.preview&&l>e.preview?i.abort():(p.data=p.data[0],h(p,s))}}}function f(t){return"greedy"===e.skipEmptyLines?""===t.join("").trim():1===t.length&&0===t[0].length}function y(){return p&&r&&(v("Delimiter","UndetectableDelimiter","Unable to auto-detect delimiting character; defaulted to '"+o.DefaultDelimiter+"'"),r=!1),e.skipEmptyLines&&(p.data=p.data.filter(function(e){return!f(e)})),b()&&function(){if(p)if(Array.isArray(p.data[0])){for(var t=0;b()&&t<p.data.length;t++)p.data[t].forEach(i);p.data.splice(0,1)}else p.data.forEach(i);function i(t,i){E(e.transformHeader)&&(t=e.transformHeader(t,i)),m.push(t)}}(),function(){if(!p||!e.header&&!e.dynamicTyping&&!e.transform)return p;function t(t,i){var r,s=e.header?{}:[];for(r=0;r<t.length;r++){var o=r,l=t[r];e.header&&(o=r>=m.length?"__parsed_extra":m[r]),e.transform&&(l=e.transform(l,o)),l=function(t,i){if(e.dynamicTypingFunction&&void 0===e.dynamicTyping[t]&&(e.dynamicTyping[t]=e.dynamicTypingFunction(t)),!0===(e.dynamicTyping[t]||e.dynamicTyping))if("true"===i||"TRUE"===i)return!0;else if("false"===i||"FALSE"===i)return!1;else if(function(e){if(n.test(e)){var t=parseFloat(e);if(t>-0x20000000000000&&t<0x20000000000000)return!0}return!1}(i))return parseFloat(i);else if(a.test(i))return new Date(i);else return""===i?null:i;return i}(o,l),"__parsed_extra"===o?(s[o]=s[o]||[],s[o].push(l)):s[o]=l}return e.header&&(r>m.length?v("FieldMismatch","TooManyFields","Too many fields: expected "+m.length+" fields but parsed "+r,u+i):r<m.length&&v("FieldMismatch","TooFewFields","Too few fields: expected "+m.length+" fields but parsed "+r,u+i)),s}var i=1;return!p.data.length||Array.isArray(p.data[0])?(p.data=p.data.map(t),i=p.data.length):p.data=t(p.data,0),e.header&&p.meta&&(p.meta.fields=m),u+=i,p}()}function b(){return e.header&&0===m.length}function v(e,t,i,r){var n={type:e,code:t,message:i};void 0!==r&&(n.row=r),p.errors.push(n)}this.parse=function(n,a,s){var l=e.quoteChar||'"';if(e.newline||(e.newline=this.guessLineEndings(n,l)),r=!1,e.delimiter)E(e.delimiter)&&(e.delimiter=e.delimiter(n),p.meta.delimiter=e.delimiter);else{var u=function(t,i,r,n,a){var s,l,u,c;a=a||[",","	","|",";",o.RECORD_SEP,o.UNIT_SEP];for(var d=0;d<a.length;d++){var m=a[d],p=0,h=0,_=0;u=void 0;for(var y=new g({comments:n,delimiter:m,newline:i,preview:10}).parse(t),b=0;b<y.data.length;b++){if(r&&f(y.data[b])){_++;continue}var v=y.data[b].length;if(h+=v,void 0===u){u=v;continue}v>0&&(p+=Math.abs(v-u),u=v)}y.data.length>0&&(h/=y.data.length-_),(void 0===l||p<=l)&&(void 0===c||h>c)&&h>1.99&&(l=p,s=m,c=h)}return e.delimiter=s,{successful:!!s,bestDelimiter:s}}(n,e.newline,e.skipEmptyLines,e.comments,e.delimitersToGuess);u.successful?e.delimiter=u.bestDelimiter:(r=!0,e.delimiter=o.DefaultDelimiter),p.meta.delimiter=e.delimiter}var d=x(e);return e.preview&&e.header&&d.preview++,t=n,p=(i=new g(d)).parse(t,a,s),y(),c?{meta:{paused:!0}}:p||{meta:{paused:!1}}},this.paused=function(){return c},this.pause=function(){c=!0,i.abort(),t=E(e.chunk)?"":t.substring(i.getCharIndex())},this.resume=function(){s.streamer._halted?(c=!1,s.streamer.parseChunk(t,!0)):setTimeout(s.resume,3)},this.aborted=function(){return d},this.abort=function(){d=!0,i.abort(),p.meta.aborted=!0,E(e.complete)&&e.complete(p),t=""},this.guessLineEndings=function(e,t){e=e.substring(0,1048576);var i=RegExp(_(t)+"([^]*?)"+_(t),"gm"),r=(e=e.replace(i,"")).split("\r"),n=e.split("\n"),a=n.length>1&&n[0].length<r[0].length;if(1===r.length||a)return"\n";for(var s=0,o=0;o<r.length;o++)"\n"===r[o][0]&&s++;return s>=r.length/2?"\r\n":"\r"}}function _(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function g(e){var t,i=(e=e||{}).delimiter,r=e.newline,n=e.comments,a=e.step,s=e.preview,l=e.fastMode,u=null,c=!1,d=t=void 0===e.quoteChar||null===e.quoteChar?'"':e.quoteChar;if(void 0!==e.escapeChar&&(d=e.escapeChar),("string"!=typeof i||o.BAD_DELIMITERS.indexOf(i)>-1)&&(i=","),n===i)throw Error("Comment character same as delimiter");!0===n?n="#":("string"!=typeof n||o.BAD_DELIMITERS.indexOf(n)>-1)&&(n=!1),"\n"!==r&&"\r"!==r&&"\r\n"!==r&&(r="\n");var m=0,p=!1;this.parse=function(o,h,f){if("string"!=typeof o)throw Error("Input must be a string");var g=o.length,y=i.length,b=r.length,v=n.length,x=E(a);m=0;var S=[],w=[],R=[],N=0;if(!o)return V();if(l||!1!==l&&-1===o.indexOf(t)){for(var C=o.split(r),P=0;P<C.length;P++){if(R=C[P],m+=R.length,P!==C.length-1)m+=r.length;else if(f)break;if(!n||R.substring(0,v)!==n){if(x){if(S=[],M(R.split(i)),q(),p)return V()}else M(R.split(i));if(s&&P>=s)return S=S.slice(0,s),V(!0)}}return V()}for(var A=o.indexOf(i,m),I=o.indexOf(r,m),T=RegExp(_(d)+_(t),"g"),j=o.indexOf(t,m);;){if(o[m]===t){for(j=m,m++;;){if(-1===(j=o.indexOf(t,j+1)))return f||w.push({type:"Quotes",code:"MissingQuotes",message:"Quoted field unterminated",row:S.length,index:m}),L();if(j===g-1)return L(o.substring(m,j).replace(T,t));if(t===d&&o[j+1]===d){j++;continue}if(t===d||0===j||o[j-1]!==d){-1!==A&&A<j+1&&(A=o.indexOf(i,j+1)),-1!==I&&I<j+1&&(I=o.indexOf(r,j+1));var k=D(-1===I?A:Math.min(A,I));if(o.substr(j+1+k,y)===i){R.push(o.substring(m,j).replace(T,t)),m=j+1+k+y,o[j+1+k+y]!==t&&(j=o.indexOf(t,m)),A=o.indexOf(i,m),I=o.indexOf(r,m);break}var O=D(I);if(o.substring(j+1+O,j+1+O+b)===r){if(R.push(o.substring(m,j).replace(T,t)),F(j+1+O+b),A=o.indexOf(i,m),j=o.indexOf(t,m),x&&(q(),p))return V();if(s&&S.length>=s)return V(!0);break}w.push({type:"Quotes",code:"InvalidQuotes",message:"Trailing quote on quoted field is malformed",row:S.length,index:m}),j++;continue}}continue}if(n&&0===R.length&&o.substring(m,m+v)===n){if(-1===I)return V();m=I+b,I=o.indexOf(r,m),A=o.indexOf(i,m);continue}if(-1!==A&&(A<I||-1===I)){R.push(o.substring(m,A)),m=A+y,A=o.indexOf(i,m);continue}if(-1!==I){if(R.push(o.substring(m,I)),F(I+b),x&&(q(),p))return V();if(s&&S.length>=s)return V(!0);continue}break}return L();function M(e){S.push(e),N=m}function D(e){var t=0;if(-1!==e){var i=o.substring(j+1,e);i&&""===i.trim()&&(t=i.length)}return t}function L(e){return f||(void 0===e&&(e=o.substring(m)),R.push(e),m=g,M(R),x&&q()),V()}function F(e){m=e,M(R),R=[],I=o.indexOf(r,m)}function V(t){if(e.header&&!h&&S.length&&!c){let t=S[0],i=Object.create(null),r=new Set(t),n=!1;for(let a=0;a<t.length;a++){let s=t[a];if(E(e.transformHeader)&&(s=e.transformHeader(s,a)),i[s]){let e,o=i[s];do e=`${s}_${o}`,o++;while(r.has(e));r.add(e),t[a]=e,i[s]++,n=!0,null===u&&(u={}),u[e]=s}else i[s]=1,t[a]=s;r.add(s)}n&&console.warn("Duplicate headers found and renamed."),c=!0}return{data:S,errors:w,meta:{delimiter:i,linebreak:r,aborted:p,truncated:!!t,cursor:N+(h||0),renamedHeaders:u}}}function q(){a(V()),S=[],w=[]}},this.abort=function(){p=!0},this.getCharIndex=function(){return m}}function y(e){var t=e.data,i=a[t.workerId],r=!1;if(t.error)i.userError(t.error,t.file);else if(t.results&&t.results.data){var n={abort:function(){r=!0,b(t.workerId,{data:[],errors:[],meta:{aborted:!0}})},pause:v,resume:v};if(E(i.userStep)){for(var s=0;s<t.results.data.length&&(i.userStep({data:t.results.data[s],errors:t.results.errors,meta:t.results.meta},n),!r);s++);delete t.results}else E(i.userChunk)&&(i.userChunk(t.results,n,t.file),delete t.results)}t.finished&&!r&&b(t.workerId,t.results)}function b(e,t){var i=a[e];E(i.userComplete)&&i.userComplete(t),i.terminate(),delete a[e]}function v(){throw Error("Not implemented.")}function x(e){if("object"!=typeof e||null===e)return e;var t=Array.isArray(e)?[]:{};for(var i in e)t[i]=x(e[i]);return t}function S(e,t){return function(){e.apply(t,arguments)}}function E(e){return"function"==typeof e}return n&&(t.onmessage=function(e){var i=e.data;if(void 0===o.WORKER_ID&&i&&(o.WORKER_ID=i.workerId),"string"==typeof i.input)t.postMessage({workerId:o.WORKER_ID,results:o.parse(i.input,i.config),finished:!0});else if(t.File&&i.input instanceof File||i.input instanceof Object){var r=o.parse(i.input,i.config);r&&t.postMessage({workerId:o.WORKER_ID,results:r,finished:!0})}}),c.prototype=Object.create(u.prototype),c.prototype.constructor=c,d.prototype=Object.create(u.prototype),d.prototype.constructor=d,m.prototype=Object.create(m.prototype),m.prototype.constructor=m,p.prototype=Object.create(u.prototype),p.prototype.constructor=p,"undefined"==typeof PAPA_BROWSER_CONTEXT&&(h.prototype=Object.create(u.prototype),h.prototype.constructor=h),o})?r.apply(t,[]):r)||(e.exports=n)},4431:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>u,metadata:()=>l});var r=i(7413),n=i(2202),a=i.n(n),s=i(4988),o=i.n(s);i(1135);let l={title:"Create Next App",description:"Generated by create next app"};function u({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:e})})}},4595:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>j});var r=i(687),n=i(3210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,i)=>i?i.toUpperCase():t.toLowerCase()),o=e=>{let t=s(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=(...e)=>e.filter((e,t,i)=>!!e&&""!==e.trim()&&i.indexOf(e)===t).join(" ").trim(),u=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:i=2,absoluteStrokeWidth:r,className:a="",children:s,iconNode:o,...d},m)=>(0,n.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:r?24*Number(i)/Number(t):i,className:l("lucide",a),...!s&&!u(d)&&{"aria-hidden":"true"},...d},[...o.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(s)?s:[s]])),m=(e,t)=>{let i=(0,n.forwardRef)(({className:i,...r},s)=>(0,n.createElement)(d,{ref:s,iconNode:t,className:l(`lucide-${a(o(e))}`,`lucide-${e}`,i),...r}));return i.displayName=o(e),i},p=m("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),h=m("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]),f=m("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),_=m("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),g=m("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),y=m("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),b=m("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),v=m("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),x=[{institute_code:"101",institute_short_name:"SLS-P",institute_name:"Symbiosis Law School, Pune",faculty:"LAW",city:"Pune"},{institute_code:"102",institute_short_name:"SLS-N",institute_name:"Symbiosis Law School, Noida",faculty:"LAW",city:"Noida"},{institute_code:"103",institute_short_name:"SLS-H",institute_name:"Symbiosis Law School, Hyderabad",faculty:"LAW",city:"Hyderabad"},{institute_code:"104",institute_short_name:"SLS-NG",institute_name:"Symbiosis Law School, Nagpur",faculty:"LAW",city:"Nagpur"},{institute_code:"201",institute_short_name:"SIBM-P",institute_name:"Symbiosis Institute of Business Management, Pune",faculty:"MANAGEMENT",city:"Pune"},{institute_code:"202",institute_short_name:"SIIB",institute_name:"Symbiosis Institute of International Business",faculty:"MANAGEMENT",city:"Pune"},{institute_code:"203",institute_short_name:"SIBM-B",institute_name:"Symbiosis Institute of Business Management, Bengaluru",faculty:"MANAGEMENT",city:"Bengaluru"},{institute_code:"204",institute_short_name:"SIBM-H",institute_name:"Symbiosis Institute of Business Management, Hyderabad",faculty:"MANAGEMENT",city:"Hyderabad"},{institute_code:"205",institute_short_name:"SIBM-N",institute_name:"Symbiosis Institute of Business Management, Nagpur",faculty:"MANAGEMENT",city:"Nagpur"},{institute_code:"206",institute_short_name:"SCMHRD",institute_name:"Symbiosis Centre for Management and Human Resource Development",faculty:"MANAGEMENT",city:"Pune"},{institute_code:"301",institute_short_name:"SIT",institute_name:"Symbiosis Institute of Technology",faculty:"ENGINEERING",city:"Pune"},{institute_code:"302",institute_short_name:"SITB",institute_name:"Symbiosis Institute of Technology, Bengaluru",faculty:"ENGINEERING",city:"Bengaluru"},{institute_code:"303",institute_short_name:"SITH",institute_name:"Symbiosis Institute of Technology, Hyderabad",faculty:"ENGINEERING",city:"Hyderabad"},{institute_code:"304",institute_short_name:"SITN",institute_name:"Symbiosis Institute of Technology, Nagpur",faculty:"ENGINEERING",city:"Nagpur"},{institute_code:"401",institute_short_name:"SICSR",institute_name:"Symbiosis Institute of Computer Studies and Research",faculty:"COMPUTER_SCIENCE",city:"Pune"},{institute_code:"402",institute_short_name:"SICSR-N",institute_name:"Symbiosis Institute of Computer Studies and Research, Nagpur",faculty:"COMPUTER_SCIENCE",city:"Nagpur"},{institute_code:"501",institute_short_name:"SIMC",institute_name:"Symbiosis Institute of Media and Communication",faculty:"MEDIA",city:"Pune"},{institute_code:"502",institute_short_name:"SIMC-B",institute_name:"Symbiosis Institute of Media and Communication, Bengaluru",faculty:"MEDIA",city:"Bengaluru"},{institute_code:"601",institute_short_name:"SID",institute_name:"Symbiosis Institute of Design",faculty:"DESIGN",city:"Pune"},{institute_code:"701",institute_short_name:"SIU-CON",institute_name:"Symbiosis College of Nursing",faculty:"HEALTH_SCIENCES",city:"Pune"},{institute_code:"702",institute_short_name:"SIHSRC",institute_name:"Symbiosis Institute of Health Sciences Research Centre",faculty:"HEALTH_SCIENCES",city:"Pune"},{institute_code:"801",institute_short_name:"SITM",institute_name:"Symbiosis Institute of Telecom Management",faculty:"TELECOM",city:"Pune"},{institute_code:"901",institute_short_name:"SIOM",institute_name:"Symbiosis Institute of Operations Management",faculty:"OPERATIONS",city:"Nashik"},{institute_code:"1001",institute_short_name:"SIS",institute_name:"Symbiosis Statistical Institute",faculty:"STATISTICS",city:"Pune"},{institute_code:"1101",institute_short_name:"SSE",institute_name:"Symbiosis School of Economics",faculty:"ECONOMICS",city:"Pune"},{institute_code:"1201",institute_short_name:"SSBF",institute_name:"Symbiosis School of Banking and Finance",faculty:"FINANCE",city:"Pune"},{institute_code:"1301",institute_short_name:"SSLA",institute_name:"Symbiosis School of Liberal Arts",faculty:"LIBERAL_ARTS",city:"Pune"},{institute_code:"1401",institute_short_name:"SICA",institute_name:"Symbiosis Institute of Culinary Arts",faculty:"CULINARY",city:"Pune"},{institute_code:"1501",institute_short_name:"SICG",institute_name:"Symbiosis Institute of Geoinformatics",faculty:"GEOINFORMATICS",city:"Pune"},{institute_code:"1601",institute_short_name:"SSIIS",institute_name:"Symbiosis School of International Studies",faculty:"INTERNATIONAL_STUDIES",city:"Pune"},{institute_code:"1701",institute_short_name:"SSSS",institute_name:"Symbiosis School of Sports Sciences",faculty:"SPORTS",city:"Pune"},{institute_code:"1801",institute_short_name:"SSOP",institute_name:"Symbiosis School of Photography",faculty:"PHOTOGRAPHY",city:"Pune"},{institute_code:"1901",institute_short_name:"SSPAD",institute_name:"Symbiosis School of Planning Architecture and Design",faculty:"ARCHITECTURE",city:"Pune"},{institute_code:"2001",institute_short_name:"SSBS",institute_name:"Symbiosis School of Biological Sciences",faculty:"BIOLOGICAL_SCIENCES",city:"Pune"},{institute_code:"2101",institute_short_name:"SSPA",institute_name:"Symbiosis School of Performing Arts",faculty:"PERFORMING_ARTS",city:"Pune"},{institute_code:"2201",institute_short_name:"SSPU",institute_name:"Symbiosis Skills and Professional University",faculty:"SKILLS",city:"Pune"},{institute_code:"2301",institute_short_name:"SCDL",institute_name:"Symbiosis Centre for Distance Learning",faculty:"DISTANCE_LEARNING",city:"Pune"},{institute_code:"2401",institute_short_name:"SCIT",institute_name:"Symbiosis Centre for Information Technology",faculty:"INFORMATION_TECHNOLOGY",city:"Pune"},{institute_code:"2501",institute_short_name:"SCRI",institute_name:"Symbiosis Centre for Research and Innovation",faculty:"RESEARCH",city:"Pune"},{institute_code:"2601",institute_short_name:"SIS-P",institute_name:"Symbiosis International School, Pune",faculty:"SCHOOL",city:"Pune"},{institute_code:"2701",institute_short_name:"SCCE",institute_name:"Symbiosis Centre for Corporate Education",faculty:"CORPORATE_EDUCATION",city:"Pune"},{institute_code:"2801",institute_short_name:"SIFIL",institute_name:"Symbiosis Institute of Foreign and Indian Languages",faculty:"LANGUAGES",city:"Pune"},{institute_code:"2901",institute_short_name:"SCMS-P",institute_name:"Symbiosis Centre for Management Studies, Pune",faculty:"MANAGEMENT",city:"Pune"},{institute_code:"2902",institute_short_name:"SCMS-N",institute_name:"Symbiosis Centre for Management Studies, Noida",faculty:"MANAGEMENT",city:"Noida"}],S=[{metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",criteria_number:"1.1"},{metric_number:"1.1.1a",metric_description:"Global developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.1b",metric_description:"National developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.1c",metric_description:"Regional developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.1d",metric_description:"Local developmental needs which the curricula address",criteria_number:"1.1"},{metric_number:"1.1.3",metric_description:"Percentage of students undertaking project work/field work/internships",criteria_number:"1.1"},{metric_number:"1.3.1",metric_description:"Institution integrates crosscutting issues relevant to Professional Ethics, Gender, Human Values, Environment and Sustainability into the Curriculum",criteria_number:"1.3"},{metric_number:"1.3.1a",metric_description:"Number of courses that include focus on gender issues during the last five years",criteria_number:"1.3"},{metric_number:"1.3.2a",metric_description:"Number of value-added courses for imparting transferable and life skills offered during last five years",criteria_number:"1.3"},{metric_number:"1.3.2b",metric_description:"Number of students enrolled in the courses under 1.3.2 above",criteria_number:"1.3"},{metric_number:"1.3.4",metric_description:"Percentage of students undertaking field projects/internships/student projects",criteria_number:"1.3"},{metric_number:"1.4.1",metric_description:"Institution obtains feedback on the syllabus and its transaction at the institution from the following stakeholders",criteria_number:"1.4"},{metric_number:"1.4.2",metric_description:"Feedback process of the Institution may be classified as follows",criteria_number:"1.4"},{metric_number:"2.2.1",metric_description:"Student – Full time Teacher Ratio",criteria_number:"2.2"},{metric_number:"2.3.1",metric_description:"Student centric methods, such as experiential learning, participative learning and problem solving methodologies are used for enhancing learning experiences",criteria_number:"2.3"},{metric_number:"2.3.2",metric_description:"Teachers use ICT enabled tools for effective teaching-learning process",criteria_number:"2.3"},{metric_number:"2.3.3",metric_description:"Ratio of students to mentor for academic and other related issues",criteria_number:"2.3"},{metric_number:"2.4.4",metric_description:"Percentage of full time teachers against sanctioned posts during the last five years",criteria_number:"2.4"},{metric_number:"2.6.1",metric_description:"Programme and course outcomes for all Programmes offered by the institution are stated and displayed on website and communicated to teachers and students",criteria_number:"2.6"},{metric_number:"2.6.2",metric_description:"Attainment of Programme outcomes and course outcomes are evaluated by the institution",criteria_number:"2.6"},{metric_number:"3.1.5",metric_description:"Institution has the following facilities for research",criteria_number:"3.1"},{metric_number:"3.3.2",metric_description:"Number of workshops/seminars conducted on Research Methodology, Intellectual Property Rights (IPR) and entrepreneurship during the last five years",criteria_number:"3.3"},{metric_number:"3.3.3",metric_description:"Number of awards/recognitions received for extension activities from government/government recognised bodies during the last five years",criteria_number:"3.3"},{metric_number:"3.4.7",metric_description:"Number of registered patents (published/not published) and patents (Granted) during the last five years",criteria_number:"3.4"},{metric_number:"3.6.1",metric_description:"Extension activities are carried out in the neighborhood community, sensitizing students to social issues, for their holistic development, and impact thereof during the last five years",criteria_number:"3.6"},{metric_number:"3.6.2",metric_description:"Number of awards and recognitions received for extension activities from government/government recognised bodies during the last five years",criteria_number:"3.6"},{metric_number:"3.6.3",metric_description:"Number of extension and outreach programs conducted by the institution through NSS/NCC/Red cross/YRC etc., during the last five years",criteria_number:"3.6"},{metric_number:"3.6.4",metric_description:"Average percentage of students participating in extension activities at 3.6.3 above during last five years",criteria_number:"3.6"},{metric_number:"3.7.1",metric_description:"Number of Collaborative activities for research, Faculty exchange, Student exchange/internship during the last five years",criteria_number:"3.7"},{metric_number:"4.1.1",metric_description:"The Institution has adequate infrastructure and physical facilities for teaching-learning. viz., classrooms, laboratories, computing equipment etc.",criteria_number:"4.1"},{metric_number:"4.3.3",metric_description:"Bandwidth of internet connection in the Institution",criteria_number:"4.3"},{metric_number:"4.3.5",metric_description:"Institution has the following Facilities for e-content development",criteria_number:"4.3"},{metric_number:"5.1.2",metric_description:"Number of capability enhancement and development schemes such as Soft skill development, Remedial coaching, Language lab, Bridge courses, Yoga, Meditation, Personal Counselling and Mentoring etc., for students",criteria_number:"5.1"},{metric_number:"5.1.3a",metric_description:"Number of students benefitted by guidance for competitive examinations and career counselling offered by the Institution during the last five years",criteria_number:"5.1"},{metric_number:"5.1.3b",metric_description:"Number of students who have passed in the qualifying examination conducted by national/state/government or other recognized bodies during the last five years",criteria_number:"5.1"},{metric_number:"5.2.1",metric_description:"Average percentage of placement of outgoing students during the last five years",criteria_number:"5.2"},{metric_number:"5.2.2",metric_description:"Average percentage of students progressing to higher education during the last five years",criteria_number:"5.2"},{metric_number:"5.2.3",metric_description:"Average percentage of students qualifying in state/national/international level examinations during the last five years",criteria_number:"5.2"},{metric_number:"5.3.1",metric_description:"Number of awards/medals won by students for outstanding performance in sports/cultural activities at inter-university/state/national/international level during the last five years",criteria_number:"5.3"},{metric_number:"5.3.2",metric_description:"Institution facilitates students representation and engagement in various administrative, co-curricular and extracurricular activities",criteria_number:"5.3"},{metric_number:"5.3.3",metric_description:"Average number of sports and cultural programs in which students of the Institution participated during last five years",criteria_number:"5.3"},{metric_number:"5.4.1",metric_description:"There is a registered Alumni Association that contributes significantly to the development of the institution through financial and/or other support services",criteria_number:"5.4"},{metric_number:"6.3.3",metric_description:"Average number of professional development/administrative training programs organized by the institution for teaching and non-teaching staff during the last five years",criteria_number:"6.3"},{metric_number:"6.3.4",metric_description:"Average percentage of teachers undergoing online/face-to-face Faculty Development Programmes (FDP) during the last five years",criteria_number:"6.3"},{metric_number:"6.5.2",metric_description:"The institution reviews its teaching learning process, structures & methodologies of operations and learning outcomes at periodic intervals through IQAC set up as per norms and recorded the incremental improvement in various activities",criteria_number:"6.5"},{metric_number:"7.1.1",metric_description:"Measures initiated by the Institution for the promotion of gender equity during the last five years",criteria_number:"7.1"},{metric_number:"7.1.8",metric_description:"Average percentage of expenditure for infrastructure augmentation excluding salary during the last five years",criteria_number:"7.1"},{metric_number:"7.1.9",metric_description:"Sensitization of students and employees of the Institution to the constitutional obligations: values, rights, duties and responsibilities of citizens",criteria_number:"7.1"},{metric_number:"7.1.10",metric_description:"The Institution has a prescribed code of conduct for students, teachers, administrators and other staff and conducts periodic programmes in this regard",criteria_number:"7.1"},{metric_number:"7.1.11",metric_description:"Institution celebrates/organizes national and international commemorative days, events and festivals",criteria_number:"7.1"},{metric_number:"7.2.1",metric_description:"Describe two best practices successfully implemented by the Institution as per NAAC format provided in the Manual",criteria_number:"7.2"},{metric_number:"7.3.1",metric_description:"Portray the performance of the Institution in one area distinctive to its priority and thrust within a maximum of 500 words",criteria_number:"7.3"}],E=e=>S.find(t=>t.metric_number===e),w=[{id:"rule_001",metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",column_name:"Programme_Name",is_required:!0,data_type:"text",validation_type:"required_field",validation_params:"",error_message:"Programme Name is required"},{id:"rule_002",metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",column_name:"Academic_Year",is_required:!0,data_type:"text",validation_type:"year_range",validation_params:"2019-2024",error_message:"Academic Year must be between 2019-2024"},{id:"rule_003",metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",column_name:"Institute_Code",is_required:!0,data_type:"text",validation_type:"institute_code_choice",validation_params:"",reference_table:"institutions",error_message:"Invalid Institute Code"},{id:"rule_004",metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",column_name:"Institute_Name",is_required:!0,data_type:"text",validation_type:"institute_name_choice",validation_params:"",reference_table:"institutions",error_message:"Invalid Institute Name"},{id:"rule_005",metric_number:"1.1.1",metric_description:"Curricula developed and implemented have relevance to the local, national, regional and global developmental needs",column_name:"Faculty",is_required:!0,data_type:"text",validation_type:"faculty_choice",validation_params:"LAW,MANAGEMENT,ENGINEERING,COMPUTER_SCIENCE,MEDIA,DESIGN,HEALTH_SCIENCES,TELECOM,OPERATIONS,STATISTICS,ECONOMICS,FINANCE,LIBERAL_ARTS,CULINARY,GEOINFORMATICS,INTERNATIONAL_STUDIES,SPORTS,PHOTOGRAPHY,ARCHITECTURE,BIOLOGICAL_SCIENCES,PERFORMING_ARTS,SKILLS,DISTANCE_LEARNING,INFORMATION_TECHNOLOGY,RESEARCH,SCHOOL,CORPORATE_EDUCATION,LANGUAGES",error_message:"Invalid Faculty selection"},{id:"rule_006",metric_number:"1.1.1a",metric_description:"Global developmental needs which the curricula address",column_name:"Global_Need_Description",is_required:!0,data_type:"text",validation_type:"word_limit",validation_params:"500",error_message:"Global Need Description must not exceed 500 words"},{id:"rule_007",metric_number:"1.1.1a",metric_description:"Global developmental needs which the curricula address",column_name:"Evidence_Document",is_required:!0,data_type:"file",validation_type:"pdf_file",validation_params:"",error_message:"Evidence document must be a PDF file"},{id:"rule_008",metric_number:"1.1.1b",metric_description:"National developmental needs which the curricula address",column_name:"National_Need_Description",is_required:!0,data_type:"text",validation_type:"word_limit",validation_params:"500",error_message:"National Need Description must not exceed 500 words"},{id:"rule_009",metric_number:"1.1.3",metric_description:"Percentage of students undertaking project work/field work/internships",column_name:"Total_Students",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:10000",error_message:"Total Students must be a number between 1 and 10000"},{id:"rule_010",metric_number:"1.1.3",metric_description:"Percentage of students undertaking project work/field work/internships",column_name:"Students_With_Projects",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:0,max:10000",error_message:"Students with Projects must be a number between 0 and 10000"},{id:"rule_011",metric_number:"1.3.1",metric_description:"Institution integrates crosscutting issues relevant to Professional Ethics, Gender, Human Values, Environment and Sustainability",column_name:"Professional_Ethics",is_required:!0,data_type:"text",validation_type:"yes_no_choice",validation_params:"Yes,No",error_message:"Professional Ethics must be Yes or No"},{id:"rule_012",metric_number:"1.3.1",metric_description:"Institution integrates crosscutting issues relevant to Professional Ethics, Gender, Human Values, Environment and Sustainability",column_name:"Gender_Issues",is_required:!0,data_type:"text",validation_type:"yes_no_choice",validation_params:"Yes,No",error_message:"Gender Issues must be Yes or No"},{id:"rule_013",metric_number:"2.2.1",metric_description:"Student – Full time Teacher Ratio",column_name:"Total_Students_Enrolled",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:50000",error_message:"Total Students Enrolled must be between 1 and 50000"},{id:"rule_014",metric_number:"2.2.1",metric_description:"Student – Full time Teacher Ratio",column_name:"Total_Fulltime_Teachers",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:5000",error_message:"Total Fulltime Teachers must be between 1 and 5000"},{id:"rule_015",metric_number:"3.3.2",metric_description:"Number of workshops/seminars conducted on Research Methodology, Intellectual Property Rights (IPR) and entrepreneurship",column_name:"Workshop_Title",is_required:!0,data_type:"text",validation_type:"text_length",validation_params:"min:10,max:200",error_message:"Workshop Title must be between 10 and 200 characters"},{id:"rule_016",metric_number:"3.3.2",metric_description:"Number of workshops/seminars conducted on Research Methodology, Intellectual Property Rights (IPR) and entrepreneurship",column_name:"Date_Conducted",is_required:!0,data_type:"date",validation_type:"date",validation_params:"format:DD/MM/YYYY,range:01/01/2019-31/12/2024",error_message:"Date must be in DD/MM/YYYY format between 01/01/2019 and 31/12/2024"},{id:"rule_017",metric_number:"4.3.3",metric_description:"Bandwidth of internet connection in the Institution",column_name:"Bandwidth_Mbps",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:10000",error_message:"Bandwidth must be between 1 and 10000 Mbps"},{id:"rule_018",metric_number:"4.3.3",metric_description:"Bandwidth of internet connection in the Institution",column_name:"Service_Provider",is_required:!0,data_type:"text",validation_type:"required_field",validation_params:"",error_message:"Service Provider is required"},{id:"rule_019",metric_number:"5.2.1",metric_description:"Average percentage of placement of outgoing students during the last five years",column_name:"Students_Eligible_For_Placement",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:0,max:10000",error_message:"Students Eligible for Placement must be between 0 and 10000"},{id:"rule_020",metric_number:"5.2.1",metric_description:"Average percentage of placement of outgoing students during the last five years",column_name:"Students_Placed",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:0,max:10000",error_message:"Students Placed must be between 0 and 10000"},{id:"rule_021",metric_number:"6.3.4",metric_description:"Average percentage of teachers undergoing online/face-to-face Faculty Development Programmes (FDP)",column_name:"FDP_Type",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Online,Face-to-Face,Hybrid",error_message:"FDP Type must be Online, Face-to-Face, or Hybrid"},{id:"rule_022",metric_number:"6.3.4",metric_description:"Average percentage of teachers undergoing online/face-to-face Faculty Development Programmes (FDP)",column_name:"Duration_Hours",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:720",error_message:"Duration must be between 1 and 720 hours"},{id:"rule_023",metric_number:"7.1.8",metric_description:"Average percentage of expenditure for infrastructure augmentation excluding salary",column_name:"Infrastructure_Expenditure",is_required:!0,data_type:"currency",validation_type:"currency",validation_params:"min:0,max:100000000",error_message:"Infrastructure Expenditure must be a valid currency amount"},{id:"rule_024",metric_number:"7.1.8",metric_description:"Average percentage of expenditure for infrastructure augmentation excluding salary",column_name:"Total_Expenditure",is_required:!0,data_type:"currency",validation_type:"currency",validation_params:"min:1,max:1000000000",error_message:"Total Expenditure must be a valid currency amount"},{id:"rule_025",metric_number:"1.3.1a",metric_description:"Number of courses that include focus on gender issues during the last five years",column_name:"Course_Name",is_required:!0,data_type:"text",validation_type:"required_field",validation_params:"",error_message:"Course Name is required"},{id:"rule_026",metric_number:"1.3.1a",metric_description:"Number of courses that include focus on gender issues during the last five years",column_name:"Gender_Focus_Description",is_required:!0,data_type:"text",validation_type:"word_limit",validation_params:"200",error_message:"Gender Focus Description must not exceed 200 words"},{id:"rule_027",metric_number:"1.3.2a",metric_description:"Number of value-added courses for imparting transferable and life skills",column_name:"Course_Type",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Transferable Skills,Life Skills,Professional Skills,Communication Skills,Leadership Skills",error_message:"Course Type must be one of the specified skill categories"},{id:"rule_028",metric_number:"1.3.2a",metric_description:"Number of value-added courses for imparting transferable and life skills",column_name:"Duration_Hours",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:500",error_message:"Duration must be between 1 and 500 hours"},{id:"rule_029",metric_number:"1.3.2b",metric_description:"Number of students enrolled in the courses under 1.3.2 above",column_name:"Students_Enrolled",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:1000",error_message:"Students Enrolled must be between 1 and 1000"},{id:"rule_030",metric_number:"1.3.4",metric_description:"Percentage of students undertaking field projects/internships/student projects",column_name:"Project_Type",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Field Project,Internship,Student Project,Research Project",error_message:"Project Type must be Field Project, Internship, Student Project, or Research Project"},{id:"rule_031",metric_number:"1.4.1",metric_description:"Institution obtains feedback on the syllabus and its transaction from stakeholders",column_name:"Stakeholder_Type",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Students,Teachers,Employers,Alumni,Parents",error_message:"Stakeholder Type must be Students, Teachers, Employers, Alumni, or Parents"},{id:"rule_032",metric_number:"1.4.1",metric_description:"Institution obtains feedback on the syllabus and its transaction from stakeholders",column_name:"Feedback_Method",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Online Survey,Physical Form,Interview,Focus Group,Email",error_message:"Feedback Method must be one of the specified methods"},{id:"rule_033",metric_number:"1.4.2",metric_description:"Feedback process of the Institution classification",column_name:"Process_Type",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"A. Feedback collected, analysed and action taken and feedback available on website,B. Feedback collected, analysed and action has been taken,C. Feedback collected and analysed,D. Feedback collected,E. Feedback not obtained",error_message:"Process Type must be one of the specified feedback process classifications"},{id:"rule_034",metric_number:"2.3.1",metric_description:"Student centric methods for enhancing learning experiences",column_name:"Teaching_Method",is_required:!0,data_type:"text",validation_type:"contains_text",validation_params:"experiential,participative,problem solving,case study,simulation,role play",error_message:"Teaching Method must contain student-centric learning approaches"},{id:"rule_035",metric_number:"2.3.2",metric_description:"Teachers use ICT enabled tools for effective teaching-learning process",column_name:"ICT_Tool",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"LMS,Video Conferencing,Interactive Whiteboard,Educational Software,Online Assessment,Virtual Lab,Mobile App,E-books",error_message:"ICT Tool must be one of the specified technology tools"},{id:"rule_036",metric_number:"2.3.3",metric_description:"Ratio of students to mentor for academic and other related issues",column_name:"Total_Mentors",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:500",error_message:"Total Mentors must be between 1 and 500"},{id:"rule_037",metric_number:"2.4.4",metric_description:"Percentage of full time teachers against sanctioned posts during the last five years",column_name:"Sanctioned_Posts",is_required:!0,data_type:"numeric",validation_type:"numeric",validation_params:"min:1,max:1000",error_message:"Sanctioned Posts must be between 1 and 1000"},{id:"rule_038",metric_number:"2.6.1",metric_description:"Programme and course outcomes displayed on website and communicated",column_name:"Website_URL",is_required:!0,data_type:"url",validation_type:"url_pattern",validation_params:"",error_message:"Website URL must be a valid URL"},{id:"rule_039",metric_number:"2.6.1",metric_description:"Programme and course outcomes displayed on website and communicated",column_name:"Communication_Method",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Website,Student Handbook,Orientation Program,Course Syllabus,Notice Board",error_message:"Communication Method must be one of the specified methods"},{id:"rule_040",metric_number:"2.6.2",metric_description:"Attainment of Programme outcomes and course outcomes are evaluated",column_name:"Evaluation_Method",is_required:!0,data_type:"text",validation_type:"exact_match",validation_params:"Direct Assessment,Indirect Assessment,Course Exit Survey,Alumni Survey,Employer Survey",error_message:"Evaluation Method must be one of the specified assessment methods"},{id:"rule_041",metric_number:"ALL",metric_description:"Common validation for all metrics",column_name:"Remarks",is_required:!1,data_type:"text",validation_type:"word_limit",validation_params:"1000",error_message:"Remarks must not exceed 1000 words"},{id:"rule_042",metric_number:"ALL",metric_description:"Common validation for all metrics",column_name:"Supporting_Document_URL",is_required:!1,data_type:"url",validation_type:"url_pattern",validation_params:"",error_message:"Supporting Document URL must be a valid URL"}],R=e=>w.filter(t=>t.metric_number===e||"ALL"===t.metric_number);class N{constructor(){this.validators={year_range:this.validateYearRange.bind(this),exact_match:this.validateExactMatch.bind(this),contains_text:this.validateContainsText.bind(this),faculty_choice:this.validateFacultyChoice.bind(this),institute_code_choice:this.validateInstituteCodeChoice.bind(this),institute_shortname_choice:this.validateInstituteShortNameChoice.bind(this),institute_name_choice:this.validateInstituteNameChoice.bind(this),fixed_digits:this.validateFixedDigits.bind(this),required_field:this.validateRequiredField.bind(this),word_limit:this.validateWordLimit.bind(this),pdf_file:this.validatePdfFile.bind(this),yes_no_choice:this.validateYesNoChoice.bind(this),binary_choice:this.validateBinaryChoice.bind(this),text_length:this.validateTextLength.bind(this),year:this.validateYear.bind(this),numeric:this.validateNumeric.bind(this),currency:this.validateCurrency.bind(this),url_pattern:this.validateUrlPattern.bind(this),image_file:this.validateImageFile.bind(this),date:this.validateDate.bind(this),reference_lookup:this.validateReferenceLookup.bind(this)}}async validateCSV(e,t,i){let r=new Date,n=R(t),a=[],s=this.validateHeaders(e.headers,n);a.push(...s);for(let t=0;t<e.rows.length;t++){let i=e.rows[t],r=this.validateRow(i,e.headers,n,t+2);a.push(...r)}let o=this.generateValidationSummary(a,e.rows.length);return{isValid:0===a.filter(e=>"error"===e.severity).length,errorCount:a.filter(e=>"error"===e.severity).length,warningCount:a.filter(e=>"warning"===e.severity).length,errors:a,summary:o,processedRows:e.rows.length,timestamp:r}}validateHeaders(e,t){let i=[];for(let r of t.filter(e=>e.is_required).map(e=>e.column_name))e.includes(r)||i.push({row:1,column:r,value:"",error:`Required column '${r}' is missing from CSV headers`,rule:"required_field",severity:"error"});return i}validateRow(e,t,i,r){let n=[];for(let a of i){let i=t.indexOf(a.column_name);if(-1===i)continue;let s=e[i]||"",o=this.validators[a.validation_type];if(o){let t=o(s,a.validation_params,r,[e]);t.isValid||n.push({row:r,column:a.column_name,value:s,error:t.error||a.error_message,rule:a.validation_type,severity:a.is_required?"error":"warning"})}}return n}generateValidationSummary(e,t){let i=new Set(e.filter(e=>"error"===e.severity).map(e=>e.row)).size,r=new Set(e.filter(e=>"warning"===e.severity).map(e=>e.row)).size,n={};return e.forEach(e=>{let t=`${e.rule}: ${e.error}`;n[t]=(n[t]||0)+1}),{totalRows:t,validRows:t-i,errorRows:i,warningRows:r,criticalErrors:e.filter(e=>"error"===e.severity).slice(0,10),commonErrors:n}}validateYearRange(e,t){if(!e.trim())return{isValid:!0};let[i,r]=t.split("-").map(e=>parseInt(e)),n=parseInt(e);return isNaN(n)?{isValid:!1,error:"Invalid year format"}:n<i||n>r?{isValid:!1,error:`Year must be between ${i} and ${r}`}:{isValid:!0}}validateExactMatch(e,t){if(!e.trim())return{isValid:!0};let i=t.split(",").map(e=>e.trim());return i.includes(e.trim())?{isValid:!0}:{isValid:!1,error:`Value must be one of: ${i.join(", ")}`}}validateContainsText(e,t){if(!e.trim())return{isValid:!0};let i=t.split(",").map(e=>e.trim().toLowerCase()),r=e.toLowerCase();return i.some(e=>r.includes(e))?{isValid:!0}:{isValid:!1,error:`Text must contain one of: ${i.join(", ")}`}}validateFacultyChoice(e,t){if(!e.trim())return{isValid:!0};let i=t.split(",").map(e=>e.trim());return i.includes(e.trim())?{isValid:!0}:{isValid:!1,error:`Invalid faculty. Must be one of: ${i.join(", ")}`}}validateInstituteCodeChoice(e){return e.trim()?x.map(e=>e.institute_code).includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Invalid institute code"}:{isValid:!0}}validateInstituteShortNameChoice(e){return e.trim()?x.map(e=>e.institute_short_name).includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Invalid institute short name"}:{isValid:!0}}validateInstituteNameChoice(e){return e.trim()?x.map(e=>e.institute_name).includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Invalid institute name"}:{isValid:!0}}validateFixedDigits(e,t){if(!e.trim())return{isValid:!0};let i=parseInt(t);return/^\d+$/.test(e)&&e.length===i?{isValid:!0}:{isValid:!1,error:`Must be exactly ${i} digits`}}validateRequiredField(e){return e&&e.trim()?{isValid:!0}:{isValid:!1,error:"This field is required"}}validateWordLimit(e,t){if(!e.trim())return{isValid:!0};let i=parseInt(t),r=e.trim().split(/\s+/).length;return r>i?{isValid:!1,error:`Exceeds word limit of ${i} words (current: ${r})`}:{isValid:!0}}validatePdfFile(e){return e.trim()?e.toLowerCase().endsWith(".pdf")?{isValid:!0}:{isValid:!1,error:"File must be a PDF"}:{isValid:!0}}validateYesNoChoice(e){return e.trim()?["Yes","No","YES","NO","yes","no"].includes(e.trim())?{isValid:!0}:{isValid:!1,error:"Value must be Yes or No"}:{isValid:!0}}validateBinaryChoice(e,t){if(!e.trim())return{isValid:!0};let[i,r]=t.split(",").map(e=>e.trim());return[i,r].includes(e.trim())?{isValid:!0}:{isValid:!1,error:`Value must be ${i} or ${r}`}}validateTextLength(e,t){if(!e.trim())return{isValid:!0};let[i,r]=t.split(","),n=parseInt(i.split(":")[1]),a=parseInt(r.split(":")[1]);return e.length<n||e.length>a?{isValid:!1,error:`Text length must be between ${n} and ${a} characters`}:{isValid:!0}}validateYear(e){if(!e.trim())return{isValid:!0};let t=parseInt(e),i=new Date().getFullYear();return isNaN(t)||t<1900||t>i+10?{isValid:!1,error:`Invalid year. Must be between 1900 and ${i+10}`}:{isValid:!0}}validateNumeric(e,t){if(!e.trim())return{isValid:!0};let i=parseFloat(e);if(isNaN(i))return{isValid:!1,error:"Value must be a number"};if(t)for(let e of t.split(",")){let[t,r]=e.split(":"),n=parseFloat(r);if("min"===t&&i<n)return{isValid:!1,error:`Value must be at least ${n}`};if("max"===t&&i>n)return{isValid:!1,error:`Value must not exceed ${n}`}}return{isValid:!0}}validateCurrency(e,t){if(!e.trim())return{isValid:!0};let i=e.replace(/[₹$,\s]/g,"");return isNaN(parseFloat(i))?{isValid:!1,error:"Invalid currency format"}:this.validateNumeric(i,t)}validateUrlPattern(e){if(!e.trim())return{isValid:!0};try{return new URL(e),{isValid:!0}}catch{return{isValid:!1,error:"Invalid URL format"}}}validateImageFile(e){return e.trim()?[".jpg",".jpeg",".png",".gif",".bmp",".webp"].some(t=>e.toLowerCase().endsWith(t))?{isValid:!0}:{isValid:!1,error:"File must be an image (jpg, jpeg, png, gif, bmp, webp)"}:{isValid:!0}}validateDate(e,t){if(!e.trim())return{isValid:!0};let i=t.split(","),r="DD/MM/YYYY",n="";for(let e of i){let[t,i]=e.split(":");"format"===t&&(r=i),"range"===t&&(n=i)}let a=e.match(/^(\d{2})\/(\d{2})\/(\d{4})$/);if(!a)return{isValid:!1,error:`Date must be in ${r} format`};let[,s,o,l]=a,u=new Date(parseInt(l),parseInt(o)-1,parseInt(s));if(u.getDate()!==parseInt(s)||u.getMonth()!==parseInt(o)-1||u.getFullYear()!==parseInt(l))return{isValid:!1,error:"Invalid date"};if(n){let[e,t]=n.split("-"),[i,r,a]=e.split("/"),[s,o,l]=t.split("/"),c=new Date(parseInt(a),parseInt(r)-1,parseInt(i)),d=new Date(parseInt(l),parseInt(o)-1,parseInt(s));if(u<c||u>d)return{isValid:!1,error:`Date must be between ${e} and ${t}`}}return{isValid:!0}}validateReferenceLookup(e,t){return!e.trim(),{isValid:!0}}}var C=i(4430),P=i.n(C);class A{static async parseCSVFile(e){return new Promise((t,i)=>{P().parse(e,{header:!1,skipEmptyLines:!0,complete:r=>{try{if(r.errors.length>0)return void i(Error(`CSV parsing error: ${r.errors[0].message}`));let n=r.data;if(0===n.length)return void i(Error("CSV file is empty"));let a=n[0],s=n.slice(1),o={headers:a,rows:s,fileName:e.name,fileSize:e.size,rowCount:s.length};t(o)}catch(e){i(e)}},error:e=>{i(Error(`Failed to parse CSV: ${e.message}`))}})})}static validateCSVFile(e){return e.name.toLowerCase().endsWith(".csv")?e.size>0xa00000?{isValid:!1,error:"File size must not exceed 10MB"}:0===e.size?{isValid:!1,error:"File cannot be empty"}:{isValid:!0}:{isValid:!1,error:"File must be a CSV file"}}static convertToCSV(e,t){let i=[e,...t];return P().unparse(i)}static downloadCSV(e,t){let i=new Blob([e],{type:"text/csv;charset=utf-8;"}),r=document.createElement("a");if(void 0!==r.download){let e=URL.createObjectURL(i);r.setAttribute("href",e),r.setAttribute("download",t),r.style.visibility="hidden",document.body.appendChild(r),r.click(),document.body.removeChild(r)}}static cleanCSVData(e){let t=e.headers.map(e=>e.trim().replace(/\s+/g,"_")),i=e.rows.map(e=>e.map(e=>e?e.toString().trim():""));return{...e,headers:t,rows:i}}static getCSVStatistics(e){let t={totalRows:e.rows.length,totalColumns:e.headers.length,emptyRows:0,emptyColumns:0,columnStats:{}};return t.emptyRows=e.rows.filter(e=>e.every(e=>!e||!e.trim())).length,e.headers.forEach((i,r)=>{let n=e.rows.map(e=>e[r]||""),a=n.filter(e=>""!==e.trim()),s=new Set(a);t.columnStats[i]={filled:a.length,empty:n.length-a.length,unique:s.size}}),t}static validateCSVStructure(e,t){let i=[],r=t.filter(t=>!e.headers.includes(t));r.length>0&&i.push(`Missing required columns: ${r.join(", ")}`);let n=e.headers.filter(e=>!t.includes(e));n.length>0&&i.push(`Unexpected columns found: ${n.join(", ")}`);let a=e.rows.filter(t=>t.length!==e.headers.length);return a.length>0&&i.push(`${a.length} rows have inconsistent column count`),{isValid:0===i.length,errors:i}}static sampleCSVData(e,t=5){return{...e,rows:e.rows.slice(0,t),rowCount:Math.min(e.rowCount,t)}}static csvToTableData(e){let t=e.rows.map(t=>{let i={};return e.headers.forEach((e,r)=>{i[e]=t[r]||""}),i});return{headers:e.headers,rows:t}}static filterCSVData(e,t){let i=e.rows.filter(i=>Object.entries(t).every(([t,r])=>{let n=e.headers.indexOf(t);return -1===n||(i[n]||"").toLowerCase().includes(r.toLowerCase())}));return{...e,rows:i,rowCount:i.length}}static sortCSVData(e,t,i="asc"){let r=e.headers.indexOf(t);if(-1===r)return e;let n=[...e.rows].sort((e,t)=>{let n=e[r]||"",a=t[r]||"",s=parseFloat(n),o=parseFloat(a);return isNaN(s)||isNaN(o)?"asc"===i?n.localeCompare(a):a.localeCompare(n):"asc"===i?s-o:o-s});return{...e,rows:n}}static getUniqueColumnValues(e,t){let i=e.headers.indexOf(t);return -1===i?[]:[...new Set(e.rows.map(e=>e[i]||""))].filter(e=>""!==e.trim()).sort()}static detectColumnTypes(e){let t={};return e.headers.forEach((i,r)=>{let n=e.rows.map(e=>e[r]||"").filter(e=>""!==e.trim());if(0===n.length){t[i]="empty";return}if(n.every(e=>!isNaN(parseFloat(e)))){t[i]="numeric";return}if(n.every(e=>!isNaN(new Date(e).getTime()))){t[i]="date";return}if(n.every(e=>{try{return new URL(e),!0}catch{return!1}})){t[i]="url";return}t[i]="text"}),t}}class I{static generateTemplate(e){let t=E(e),i=R(e);if(!t)throw Error(`Metric ${e} not found`);let r=this.generateTemplateColumns(i),n=this.generateSampleData(r),a=this.generateInstructions(e,i);return{metricNumber:e,metricDescription:t.metric_description,columns:r,instructions:a,sampleData:n}}static generateTemplateColumns(e){let t=new Map;return e.forEach(e=>{t.has(e.column_name)||t.set(e.column_name,{name:e.column_name,description:this.getColumnDescription(e),dataType:e.data_type,required:e.is_required,validationHint:this.getValidationHint(e),sampleValue:this.generateSampleValue(e)})}),Array.from(t.values()).sort((e,t)=>e.required&&!t.required?-1:!e.required&&t.required?1:e.name.localeCompare(t.name))}static getColumnDescription(e){return({Programme_Name:"Name of the academic programme",Academic_Year:"Academic year in YYYY-YYYY format",Institute_Code:"Official institute code from SIU",Institute_Name:"Full name of the institute",Faculty:"Faculty/Department name",Total_Students:"Total number of students",Students_With_Projects:"Number of students with projects/internships",Global_Need_Description:"Description of global developmental needs addressed",National_Need_Description:"Description of national developmental needs addressed",Evidence_Document:"Supporting document file path",Professional_Ethics:"Whether professional ethics is integrated (Yes/No)",Gender_Issues:"Whether gender issues are addressed (Yes/No)",Workshop_Title:"Title of the workshop/seminar",Date_Conducted:"Date when workshop was conducted",Bandwidth_Mbps:"Internet bandwidth in Mbps",Service_Provider:"Internet service provider name",Students_Eligible_For_Placement:"Number of students eligible for placement",Students_Placed:"Number of students successfully placed",FDP_Type:"Type of Faculty Development Programme",Duration_Hours:"Duration of programme in hours",Infrastructure_Expenditure:"Amount spent on infrastructure",Total_Expenditure:"Total expenditure amount",Remarks:"Additional remarks or comments",Supporting_Document_URL:"URL to supporting documents"})[e.column_name]||`Data for ${e.column_name.replace(/_/g," ")}`}static getValidationHint(e){switch(e.validation_type){case"year_range":return`Year between ${e.validation_params}`;case"exact_match":return`Must be one of: ${e.validation_params}`;case"numeric":return`Numeric value ${e.validation_params?`(${e.validation_params})`:""}`;case"word_limit":return`Maximum ${e.validation_params} words`;case"text_length":return`Text length ${e.validation_params}`;case"yes_no_choice":return"Enter Yes or No";case"date":return"Date in DD/MM/YYYY format";case"currency":return"Currency amount (e.g., 100000)";case"url_pattern":return"Valid URL starting with http:// or https://";case"pdf_file":return"PDF file path or name";case"image_file":return"Image file path (jpg, png, etc.)";case"required_field":return"This field is mandatory";case"institute_code_choice":return"Valid SIU institute code";case"institute_name_choice":return"Valid SIU institute name";case"faculty_choice":return"Valid faculty name";default:return"Follow the specified format"}}static generateSampleValue(e){switch(e.validation_type){case"year_range":let[t]=e.validation_params.split("-");return t||"2023";case"exact_match":let i=e.validation_params.split(",");return i[0]?.trim()||"Option1";case"numeric":return"100";case"word_limit":return"Sample text content";case"yes_no_choice":return"Yes";case"date":return"01/01/2023";case"currency":return"100000";case"url_pattern":return"https://example.com/document.pdf";case"pdf_file":return"document.pdf";case"image_file":return"image.jpg";case"institute_code_choice":return"101";case"institute_name_choice":return"Symbiosis Law School, Pune";case"faculty_choice":return"LAW";default:return this.getDefaultSampleValue(e.column_name)}}static getDefaultSampleValue(e){return({Programme_Name:"Bachelor of Laws (LL.B.)",Academic_Year:"2023-2024",Institute_Code:"101",Institute_Name:"Symbiosis Law School, Pune",Faculty:"LAW",Total_Students:"150",Students_With_Projects:"120",Workshop_Title:"Research Methodology Workshop",Service_Provider:"Airtel Business",Bandwidth_Mbps:"100",Remarks:"Additional information if any"})[e]||"Sample Value"}static generateSampleData(e){let t=[];for(let i=0;i<3;i++){let r=e.map(e=>"Academic_Year"===e.name?`${2021+i}-${2022+i}`:e.name.includes("Total")||e.name.includes("Students")?(100+10*i).toString():e.sampleValue);t.push(r)}return t}static generateInstructions(e,t){let i=[`Template for Metric ${e}`,"Please follow these guidelines when filling the template:","","General Instructions:","• Fill all required fields (marked with *)","• Use the exact format specified for each field","• Do not modify column headers","• Ensure data consistency across all rows","• Save the file in CSV format before uploading","","Field-specific Instructions:"];return[...new Set(t.map(e=>e.validation_type))].forEach(e=>{switch(e){case"year_range":i.push("• Academic years should be in YYYY-YYYY format (e.g., 2023-2024)");break;case"institute_code_choice":i.push("• Use only valid SIU institute codes (101, 102, 201, etc.)");break;case"date":i.push("• Dates should be in DD/MM/YYYY format (e.g., 15/03/2023)");break;case"numeric":i.push("• Numeric fields should contain only numbers");break;case"currency":i.push("• Currency amounts should be in numbers only (without symbols)");break;case"yes_no_choice":i.push('• Yes/No fields should contain exactly "Yes" or "No"');break;case"word_limit":i.push("• Respect word limits for text fields");break;case"pdf_file":i.push("• File references should include .pdf extension")}}),i.push(""),i.push("For support, contact: <EMAIL>"),i}static downloadTemplate(e){let t=this.generateTemplate(e),i=t.columns.map(e=>e.name),r=A.convertToCSV(i,t.sampleData),n=`AQAR_Template_${e}_${new Date().toISOString().split("T")[0]}.csv`;A.downloadCSV(r,n)}static generateTemplateWithInstructions(e){let t=this.generateTemplate(e),i="";t.instructions.forEach(e=>{i+=`# ${e}
`}),i+="\n",i+="# Column Descriptions:\n",t.columns.forEach(e=>{let t=e.required?" (Required)":" (Optional)";i+=`# ${e.name}${t}: ${e.description}
`,i+=`#   Format: ${e.validationHint}
`,i+=`#   Example: ${e.sampleValue}
`}),i+="\n";let r=t.columns.map(e=>e.name),n=A.convertToCSV(r,t.sampleData);return i+=n}static getTemplateSummary(e){let t=this.generateTemplate(e),i=t.columns.filter(e=>e.required).length,r=[...new Set(t.columns.map(e=>e.validationHint.split(" ")[0]))];return{metricNumber:t.metricNumber,metricDescription:t.metricDescription,totalColumns:t.columns.length,requiredColumns:i,optionalColumns:t.columns.length-i,validationTypes:r}}}let T=()=>{let[e,t]=(0,n.useState)({instituteCode:"",metricNumber:"",csvFile:null}),[i,a]=(0,n.useState)(null),[s,o]=(0,n.useState)(!1),[l,u]=(0,n.useState)(null),[c,d]=(0,n.useState)(""),m=(0,n.useCallback)(e=>{let i=e.target.files?.[0];if(!i)return;let r=A.validateCSVFile(i);if(!r.isValid)return void d(r.error||"Invalid file");t(e=>({...e,csvFile:i})),d("")},[]),E=(0,n.useCallback)(async()=>{if(!e.csvFile||!e.instituteCode||!e.metricNumber)return void d("Please fill all required fields and upload a CSV file");o(!0),d("");try{let t=await A.parseCSVFile(e.csvFile);u(t);let i=new N,r=await i.validateCSV(t,e.metricNumber,e.instituteCode);a(r)}catch(e){d(e instanceof Error?e.message:"Validation failed")}finally{o(!1)}},[e]),w=(0,n.useCallback)(()=>{if(!e.metricNumber)return void d("Please select a metric first");try{I.downloadTemplate(e.metricNumber)}catch(e){d(e instanceof Error?e.message:"Failed to download template")}},[e.metricNumber]),R=(0,n.useCallback)(()=>{if(!i?.isValid)return void d("Cannot submit file with validation errors");alert("File submitted to QMB successfully!")},[i]),C=x.find(t=>t.institute_code===e.instituteCode),P=S.find(t=>t.metric_number===e.metricNumber);return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-red-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b-4 border-red-600",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"NAAC AQAR CSV Validation Tool"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 mt-2",children:"Symbiosis International University - Quality Assurance"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-red-600 rounded-full flex items-center justify-center",children:(0,r.jsx)(p,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center",children:(0,r.jsx)(h,{className:"w-6 h-6 text-white"})})]})]})})}),(0,r.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-6",children:"Validation Form"}),c&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f,{className:"w-5 h-5 text-red-500 mr-2"}),(0,r.jsx)("span",{className:"text-red-700",children:c})]})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,r.jsx)(h,{className:"w-4 h-4 inline mr-1"}),"Select Your Institution *"]}),(0,r.jsxs)("select",{value:e.instituteCode,onChange:e=>t(t=>({...t,instituteCode:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"",children:"Choose Institution..."}),x.map(e=>(0,r.jsxs)("option",{value:e.institute_code,children:[e.institute_code," - ",e.institute_short_name," - ",e.institute_name]},e.institute_code))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,r.jsx)(_,{className:"w-4 h-4 inline mr-1"}),"Select Metric *"]}),(0,r.jsxs)("select",{value:e.metricNumber,onChange:e=>t(t=>({...t,metricNumber:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"",children:"Choose Metric..."}),S.map(e=>(0,r.jsxs)("option",{value:e.metric_number,children:[e.metric_number," - ",e.metric_description]},e.metric_number))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,r.jsx)(g,{className:"w-4 h-4 inline mr-1"}),"Upload CSV File *"]}),(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors",children:[(0,r.jsx)("input",{type:"file",accept:".csv",onChange:m,className:"hidden",id:"csv-upload"}),(0,r.jsxs)("label",{htmlFor:"csv-upload",className:"cursor-pointer",children:[(0,r.jsx)(g,{className:"w-8 h-8 text-gray-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.csvFile?e.csvFile.name:"Click to upload CSV file"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Maximum file size: 10MB"})]})]})]}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,r.jsx)("button",{onClick:E,disabled:!e.csvFile||!e.instituteCode||!e.metricNumber||s,className:"flex-1 min-w-[200px] bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:s?"Validating...":"Validate CSV"}),(0,r.jsx)("button",{onClick:R,disabled:!i?.isValid,className:"flex-1 min-w-[200px] bg-green-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:"Submit to QMB"}),(0,r.jsxs)("button",{onClick:w,disabled:!e.metricNumber,className:"flex-1 min-w-[200px] bg-orange-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-orange-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:[(0,r.jsx)(y,{className:"w-4 h-4 inline mr-2"}),"Download Template"]})]})]})]})}),(0,r.jsxs)("div",{className:"space-y-6",children:[C&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Selected Institution"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Code:"})," ",C.institute_code]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Name:"})," ",C.institute_name]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Faculty:"})," ",C.faculty]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"City:"})," ",C.city]})]})]}),P&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Selected Metric"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Number:"})," ",P.metric_number]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Criteria:"})," ",P.criteria_number]}),(0,r.jsx)("p",{className:"text-gray-600",children:P.metric_description})]})]}),l&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"CSV File Info"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"File:"})," ",l.fileName]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Size:"})," ",(l.fileSize/1024).toFixed(1)," KB"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Rows:"})," ",l.rowCount]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Columns:"})," ",l.headers.length]})]})]})]})]}),i&&(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold text-gray-900",children:"Validation Results"}),(0,r.jsx)("div",{className:"flex items-center",children:i.isValid?(0,r.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,r.jsx)(b,{className:"w-6 h-6 mr-2"}),(0,r.jsx)("span",{className:"font-medium",children:"Validation Passed"})]}):(0,r.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,r.jsx)(f,{className:"w-6 h-6 mr-2"}),(0,r.jsx)("span",{className:"font-medium",children:"Validation Failed"})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:i.processedRows}),(0,r.jsx)("div",{className:"text-sm text-blue-800",children:"Total Rows"})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:i.summary.validRows}),(0,r.jsx)("div",{className:"text-sm text-green-800",children:"Valid Rows"})]}),(0,r.jsxs)("div",{className:"bg-red-50 p-4 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:i.errorCount}),(0,r.jsx)("div",{className:"text-sm text-red-800",children:"Errors"})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:i.warningCount}),(0,r.jsx)("div",{className:"text-sm text-yellow-800",children:"Warnings"})]})]}),i.errors.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Error Details"}),(0,r.jsx)("div",{className:"max-h-96 overflow-y-auto",children:(0,r.jsxs)("div",{className:"space-y-2",children:[i.errors.slice(0,50).map((e,t)=>(0,r.jsx)("div",{className:`p-3 rounded-lg border-l-4 ${"error"===e.severity?"bg-red-50 border-red-400":"bg-yellow-50 border-yellow-400"}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:["error"===e.severity?(0,r.jsx)(f,{className:"w-5 h-5 text-red-500 mr-2 mt-0.5"}):(0,r.jsx)(v,{className:"w-5 h-5 text-yellow-500 mr-2 mt-0.5"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("p",{className:"font-medium text-gray-900",children:["Row ",e.row,', Column "',e.column,'"']}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.error}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:['Value: "',e.value,'" | Rule: ',e.rule]})]})]})},t)),i.errors.length>50&&(0,r.jsxs)("div",{className:"text-center py-4 text-gray-500",children:["... and ",i.errors.length-50," more errors"]})]})})]})]})})]})]})};function j(){return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,r.jsx)(T,{})})}},4722:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return s}});let r=i(5531),n=i(5499);function a(e){return(0,r.ensureLeadingSlash)(e.split("/").reduce((e,t,i,r)=>!t||(0,n.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&i===r.length-1?e:e+"/"+t,""))}function s(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return g},NormalizeError:function(){return f},PageNotFoundError:function(){return _},SP:function(){return m},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return s},getURL:function(){return o},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,i=!1;return function(){for(var r=arguments.length,n=Array(r),a=0;a<r;a++)n[a]=arguments[a];return i||(i=!0,t=e(...n)),t}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>n.test(e);function s(){let{protocol:e,hostname:t,port:i}=window.location;return e+"//"+t+(i?":"+i:"")}function o(){let{href:e}=window.location,t=s();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let i=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(i&&u(i))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let m="undefined"!=typeof performance,p=m&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class f extends Error{}class _ extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},5348:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6346,23)),Promise.resolve().then(i.t.bind(i,7924,23)),Promise.resolve().then(i.t.bind(i,5656,23)),Promise.resolve().then(i.t.bind(i,99,23)),Promise.resolve().then(i.t.bind(i,8243,23)),Promise.resolve().then(i.t.bind(i,8827,23)),Promise.resolve().then(i.t.bind(i,2763,23)),Promise.resolve().then(i.t.bind(i,7173,23))},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var i=function(e){for(var t=[],i=0;i<e.length;){var r=e[i];if("*"===r||"+"===r||"?"===r){t.push({type:"MODIFIER",index:i,value:e[i++]});continue}if("\\"===r){t.push({type:"ESCAPED_CHAR",index:i++,value:e[i++]});continue}if("{"===r){t.push({type:"OPEN",index:i,value:e[i++]});continue}if("}"===r){t.push({type:"CLOSE",index:i,value:e[i++]});continue}if(":"===r){for(var n="",a=i+1;a<e.length;){var s=e.charCodeAt(a);if(s>=48&&s<=57||s>=65&&s<=90||s>=97&&s<=122||95===s){n+=e[a++];continue}break}if(!n)throw TypeError("Missing parameter name at "+i);t.push({type:"NAME",index:i,value:n}),i=a;continue}if("("===r){var o=1,l="",a=i+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--o){a++;break}}else if("("===e[a]&&(o++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);l+=e[a++]}if(o)throw TypeError("Unbalanced pattern at "+i);if(!l)throw TypeError("Missing pattern at "+i);t.push({type:"PATTERN",index:i,value:l}),i=a;continue}t.push({type:"CHAR",index:i,value:e[i++]})}return t.push({type:"END",index:i,value:""}),t}(e),r=t.prefixes,a=void 0===r?"./":r,s="[^"+n(t.delimiter||"/#?")+"]+?",o=[],l=0,u=0,c="",d=function(e){if(u<i.length&&i[u].type===e)return i[u++].value},m=function(e){var t=d(e);if(void 0!==t)return t;var r=i[u];throw TypeError("Unexpected "+r.type+" at "+r.index+", expected "+e)},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<i.length;){var h=d("CHAR"),f=d("NAME"),_=d("PATTERN");if(f||_){var g=h||"";-1===a.indexOf(g)&&(c+=g,g=""),c&&(o.push(c),c=""),o.push({name:f||l++,prefix:g,suffix:"",pattern:_||s,modifier:d("MODIFIER")||""});continue}var y=h||d("ESCAPED_CHAR");if(y){c+=y;continue}if(c&&(o.push(c),c=""),d("OPEN")){var g=p(),b=d("NAME")||"",v=d("PATTERN")||"",x=p();m("CLOSE"),o.push({name:b||(v?l++:""),pattern:b&&!v?s:v,prefix:g,suffix:x,modifier:d("MODIFIER")||""});continue}m("END")}return o}function i(e,t){void 0===t&&(t={});var i=a(t),r=t.encode,n=void 0===r?function(e){return e}:r,s=t.validate,o=void 0===s||s,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",i)});return function(t){for(var i="",r=0;r<e.length;r++){var a=e[r];if("string"==typeof a){i+=a;continue}var s=t?t[a.name]:void 0,u="?"===a.modifier||"*"===a.modifier,c="*"===a.modifier||"+"===a.modifier;if(Array.isArray(s)){if(!c)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===s.length){if(u)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var d=0;d<s.length;d++){var m=n(s[d],a);if(o&&!l[r].test(m))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+m+'"');i+=a.prefix+m+a.suffix}continue}if("string"==typeof s||"number"==typeof s){var m=n(String(s),a);if(o&&!l[r].test(m))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+m+'"');i+=a.prefix+m+a.suffix;continue}if(!u){var p=c?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+p)}}return i}}function r(e,t,i){void 0===i&&(i={});var r=i.decode,n=void 0===r?function(e){return e}:r;return function(i){var r=e.exec(i);if(!r)return!1;for(var a=r[0],s=r.index,o=Object.create(null),l=1;l<r.length;l++)!function(e){if(void 0!==r[e]){var i=t[e-1];"*"===i.modifier||"+"===i.modifier?o[i.name]=r[e].split(i.prefix+i.suffix).map(function(e){return n(e,i)}):o[i.name]=n(r[e],i)}}(l);return{path:a,index:s,params:o}}}function n(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function s(e,t,i){void 0===i&&(i={});for(var r=i.strict,s=void 0!==r&&r,o=i.start,l=i.end,u=i.encode,c=void 0===u?function(e){return e}:u,d="["+n(i.endsWith||"")+"]|$",m="["+n(i.delimiter||"/#?")+"]",p=void 0===o||o?"^":"",h=0;h<e.length;h++){var f=e[h];if("string"==typeof f)p+=n(c(f));else{var _=n(c(f.prefix)),g=n(c(f.suffix));if(f.pattern)if(t&&t.push(f),_||g)if("+"===f.modifier||"*"===f.modifier){var y="*"===f.modifier?"?":"";p+="(?:"+_+"((?:"+f.pattern+")(?:"+g+_+"(?:"+f.pattern+"))*)"+g+")"+y}else p+="(?:"+_+"("+f.pattern+")"+g+")"+f.modifier;else p+="("+f.pattern+")"+f.modifier;else p+="(?:"+_+g+")"+f.modifier}}if(void 0===l||l)s||(p+=m+"?"),p+=i.endsWith?"(?="+d+")":"$";else{var b=e[e.length-1],v="string"==typeof b?m.indexOf(b[b.length-1])>-1:void 0===b;s||(p+="(?:"+m+"(?="+d+"))?"),v||(p+="(?="+m+"|"+d+")")}return new RegExp(p,a(i))}function o(t,i,r){if(t instanceof RegExp){if(!i)return t;var n=t.source.match(/\((?!\?)/g);if(n)for(var l=0;l<n.length;l++)i.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,i,r).source}).join("|")+")",a(r)):s(e(t,r),i,r)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,r){return i(e(t,r),r)},t.tokensToFunction=i,t.match=function(e,t){var i=[];return r(o(e,i,t),i,t)},t.regexpToFunction=r,t.tokensToRegexp=s,t.pathToRegexp=o})(),e.exports=t})()},5526:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return d},prepareDestination:function(){return m}});let r=i(5362),n=i(3293),a=i(6759),s=i(6199),o=i(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,i,r){void 0===i&&(i=[]),void 0===r&&(r=[]);let n={},a=i=>{let r,a=i.key;switch(i.type){case"header":a=a.toLowerCase(),r=e.headers[a];break;case"cookie":r="cookies"in e?e.cookies[i.key]:(0,o.getCookieParser)(e.headers)()[i.key];break;case"query":r=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};r=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!i.value&&r)return n[function(e){let t="";for(let i=0;i<e.length;i++){let r=e.charCodeAt(i);(r>64&&r<91||r>96&&r<123)&&(t+=e[i])}return t}(a)]=r,!0;if(r){let e=RegExp("^"+i.value+"$"),t=Array.isArray(r)?r.slice(-1)[0].match(e):r.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{n[e]=t.groups[e]}):"host"===i.type&&t[0]&&(n.host=t[0])),!0}return!1};return!(!i.every(e=>a(e))||r.some(e=>a(e)))&&n}function c(e,t){if(!e.includes(":"))return e;for(let i of Object.keys(t))e.includes(":"+i)&&(e=e.replace(RegExp(":"+i+"\\*","g"),":"+i+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+i+"\\?","g"),":"+i+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+i+"\\+","g"),":"+i+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+i+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+i));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,r.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let i of Object.keys({...e.params,...e.query}))i&&(t=t.replace(RegExp(":"+(0,n.escapeStringRegexp)(i),"g"),"__ESC_COLON_"+i));let i=(0,a.parseUrl)(t),r=i.pathname;r&&(r=l(r));let s=i.href;s&&(s=l(s));let o=i.hostname;o&&(o=l(o));let u=i.hash;return u&&(u=l(u)),{...i,pathname:r,hostname:o,href:s,hash:u}}function m(e){let t,i,n=Object.assign({},e.query),a=d(e),{hostname:o,query:u}=a,m=a.pathname;a.hash&&(m=""+m+a.hash);let p=[],h=[];for(let e of((0,r.pathToRegexp)(m,h),h))p.push(e.name);if(o){let e=[];for(let t of((0,r.pathToRegexp)(o,e),e))p.push(t.name)}let f=(0,r.compile)(m,{validate:!1});for(let[i,n]of(o&&(t=(0,r.compile)(o,{validate:!1})),Object.entries(u)))Array.isArray(n)?u[i]=n.map(t=>c(l(t),e.params)):"string"==typeof n&&(u[i]=c(l(n),e.params));let _=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!_.some(e=>p.includes(e)))for(let t of _)t in u||(u[t]=e.params[t]);if((0,s.isInterceptionRouteAppPath)(m))for(let t of m.split("/")){let i=s.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(i){"(..)(..)"===i?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=i;break}}try{let[r,n]=(i=f(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=r,a.hash=(n?"#":"")+(n||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...n,...a.query},{newUrl:i,destQuery:u,parsedDestination:a}}},5531:(e,t)=>{"use strict";function i(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return i}})},6199:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return n},extractInterceptionRouteInformation:function(){return s},isInterceptionRouteAppPath:function(){return a}});let r=i(4722),n=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>n.find(t=>e.startsWith(t)))}function s(e){let t,i,a;for(let r of e.split("/"))if(i=n.find(e=>r.startsWith(e))){[t,a]=e.split(i,2);break}if(!t||!i||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,r.normalizeAppPath)(t),i){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let s=t.split("/");if(s.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=s.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},6341:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{getPreviouslyRevalidatedTags:function(){return g},getUtils:function(){return _},interpolateDynamicPath:function(){return h},normalizeDynamicRouteParams:function(){return f},normalizeVercelUrl:function(){return p}});let r=i(9551),n=i(1959),a=i(2437),s=i(4396),o=i(8034),l=i(5526),u=i(2887),c=i(4722),d=i(6143),m=i(7912);function p(e,t,i){let n=(0,r.parse)(e.url,!0);for(let e of(delete n.search,Object.keys(n.query))){let r=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),a=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(r||a||t.includes(e)||i&&Object.keys(i.groups).includes(e))&&delete n.query[e]}e.url=(0,r.format)(n)}function h(e,t,i){if(!i)return e;for(let r of Object.keys(i.groups)){let n,{optional:a,repeat:s}=i.groups[r],o=`[${s?"...":""}${r}]`;a&&(o=`[${o}]`);let l=t[r];n=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,n)}return e}function f(e,t,i,r){let n={};for(let a of Object.keys(t.groups)){let s=e[a];"string"==typeof s?s=(0,c.normalizeRscURL)(s):Array.isArray(s)&&(s=s.map(c.normalizeRscURL));let o=i[a],l=t.groups[a].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(s)?s.some(t=>t.includes(e)):null==s?void 0:s.includes(e)):null==s?void 0:s.includes(o))||void 0===s&&!(l&&r))return{params:{},hasValidParams:!1};l&&(!s||Array.isArray(s)&&1===s.length&&("index"===s[0]||s[0]===`[[...${a}]]`))&&(s=void 0,delete e[a]),s&&"string"==typeof s&&t.groups[a].repeat&&(s=s.split("/")),s&&(n[a]=s)}return{params:n,hasValidParams:!0}}function _({page:e,i18n:t,basePath:i,rewrites:r,pageIsDynamic:c,trailingSlash:d,caseSensitive:_}){let g,y,b;return c&&(g=(0,s.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),b=(y=(0,o.getRouteMatcher)(g))(e)),{handleRewrites:function(s,o){let m={},p=o.pathname,h=r=>{let u=(0,a.getPathMatch)(r.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!_});if(!o.pathname)return!1;let h=u(o.pathname);if((r.has||r.missing)&&h){let e=(0,l.matchHas)(s,o.query,r.has,r.missing);e?Object.assign(h,e):h=!1}if(h){let{parsedDestination:a,destQuery:s}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:h,query:o.query});if(a.protocol)return!0;if(Object.assign(m,s,h),Object.assign(o.query,a.query),delete a.query,Object.assign(o,a),!(p=o.pathname))return!1;if(i&&(p=p.replace(RegExp(`^${i}`),"")||"/"),t){let e=(0,n.normalizeLocalePath)(p,t.locales);p=e.pathname,o.query.nextInternalLocale=e.detectedLocale||h.nextInternalLocale}if(p===e)return!0;if(c&&y){let e=y(p);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of r.beforeFiles||[])h(e);if(p!==e){let t=!1;for(let e of r.afterFiles||[])if(t=h(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(p||"");return t===(0,u.removeTrailingSlash)(e)||(null==y?void 0:y(t))})()){for(let e of r.fallback||[])if(t=h(e))break}}return m},defaultRouteRegex:g,dynamicRouteMatcher:y,defaultRouteMatches:b,getParamsFromRouteMatches:function(e){if(!g)return null;let{groups:t,routeKeys:i}=g,r=(0,o.getRouteMatcher)({re:{exec:e=>{let r=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(r)){let i=(0,m.normalizeNextQueryParam)(e);i&&(r[i]=t,delete r[e])}let n={};for(let e of Object.keys(i)){let a=i[e];if(!a)continue;let s=t[a],o=r[e];if(!s.optional&&!o)return null;n[s.pos]=o}return n}},groups:t})(e);return r||null},normalizeDynamicRouteParams:(e,t)=>g&&b?f(e,g,b,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>p(e,t,g),interpolateDynamicPath:(e,t)=>h(e,t,g)}}function g(e,t){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6355:(e,t,i)=>{Promise.resolve().then(i.bind(i,1204))},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,i){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var n={},a=t.split(r),s=(i||{}).decode||e,o=0;o<a.length;o++){var l=a[o],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==n[c]&&(n[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,s))}}return n},t.serialize=function(e,t,r){var a=r||{},s=a.encode||i;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var o=s(t);if(o&&!n.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=a.maxAge){var u=a.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(a.domain){if(!n.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!n.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,i=encodeURIComponent,r=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6759:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let r=i(2785),n=i(3736);function a(e){if(e.startsWith("/"))return(0,n.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,r.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},6788:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,6444,23)),Promise.resolve().then(i.t.bind(i,6042,23)),Promise.resolve().then(i.t.bind(i,8170,23)),Promise.resolve().then(i.t.bind(i,9477,23)),Promise.resolve().then(i.t.bind(i,9345,23)),Promise.resolve().then(i.t.bind(i,2089,23)),Promise.resolve().then(i.t.bind(i,6577,23)),Promise.resolve().then(i.t.bind(i,1307,23))},7910:e=>{"use strict";e.exports=require("stream")},8034:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return n}});let r=i(4827);function n(e){let{re:t,groups:i}=e;return e=>{let n=t.exec(e);if(!n)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new r.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},s={};for(let[e,t]of Object.entries(i)){let i=n[t.pos];void 0!==i&&(t.repeat?s[e]=i.split("/").map(e=>a(e)):s[e]=a(i))}return s}}},8212:(e,t,i)=>{"use strict";function r(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:r}=i(6415);return r(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return r}})},8304:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return s},getExtensionRegexString:function(){return l},isMetadataPage:function(){return d},isMetadataRoute:function(){return m},isMetadataRouteFile:function(){return u},isStaticMetadataRoute:function(){return c}});let r=i(2958),n=i(4722),a=i(554),s={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function u(e,t,i){let n=(i?"":"?")+"$",a=`\\d?${i?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${n}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${n}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${n}`),RegExp(`[\\\\/]${s.icon.filename}${a}${l(s.icon.extensions,t)}${n}`),RegExp(`[\\\\/]${s.apple.filename}${a}${l(s.apple.extensions,t)}${n}`),RegExp(`[\\\\/]${s.openGraph.filename}${a}${l(s.openGraph.extensions,t)}${n}`),RegExp(`[\\\\/]${s.twitter.filename}${a}${l(s.twitter.extensions,t)}${n}`)],u=(0,r.normalizePathSep)(e);return o.some(e=>e.test(u))}function c(e){let t=e.replace(/\/route$/,"");return(0,a.isAppRouteRoute)(e)&&u(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function d(e){return!(0,a.isAppRouteRoute)(e)&&u(e,[],!1)}function m(e){let t=(0,n.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,a.isAppRouteRoute)(e)&&u(t,[],!1)}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9912:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[447,169],()=>i(3818));module.exports=r})();