# Simple NAAC AQAR CSV Validation Tool - Development Prompt

## ⚠️ CRITICAL IMPLEMENTATION REQUIREMENTS

**MANDATORY STANDARDS:**
- **NO ASSUMPTIONS**: Follow exact specifications provided
- **NO OMISSIONS**: Implement ALL 928 validation rules and 53 metrics
- **NO PARTIAL IMPLEMENTATIONS**: Complete working functionality only
- **NO EXTERNAL APIs**: Use only built-in Google Apps Script services
- **SIMPLE BUT COMPLETE**: Easy to use interface with comprehensive validation

## Project Overview

Develop a simple, user-friendly NAAC AQAR CSV validation web app using Google Apps Script that allows any SIU institution staff to validate their CSV data against predefined rules and submit error-free files to QMB.

## System Architecture

### 1. Backend Setup (Auto-Generated Google Sheets)

#### Sheet 1: "Validation_Rules" (Auto-import from provided CSV)
- **CRITICAL**: Import ALL 928 validation rules exactly as provided
- Columns: metric_number, metric_description, column_name, is_required, data_type, validation_type, validation_params, reference_table, error_message
- This drives all validation logic

#### Sheet 2: "Institution_List" (Auto-populate from provided reference)
- **ALL 43 SIU INSTITUTIONS** must be included:
```
101, SLS-P, Symbiosis Law School, Pune, LAW, Pune
102, SLS-N, Symbiosis Law School, Noida, LAW, Noida
103, SLS-H, Symbiosis Law School, Hyderabad, LAW, Hyderabad
104, SLS-NG, Symbiosis Law School, Nagpur, LAW, Nagpur
201, SIBM-P, Symbiosis Institute of Business Management, Pune, MANAGEMENT, Pune
202, SIIB, Symbiosis Institute of International Business, MANAGEMENT, Pune
[... continue for all 43 institutions exactly as provided]
```
- Columns: institute_code, institute_short_name, institute_name, faculty, city

#### Sheet 3: "Metrics_List" (Auto-generate from validation rules)
- **ALL 53 METRICS** extracted from validation rules:
```
1.1.1, 1.1.1a, 1.1.1b, 1.1.1c, 1.1.1d, 1.1.3, 1.3.1, 1.3.1a, 1.3.2a, 1.3.2b, 
1.3.4, 1.4.1, 1.4.2, 2.2.1, 2.3.1, 2.3.2, 2.3.3, 2.4.4, 2.6.1, 2.6.2, 
3.1.5, 3.3.2, 3.3.3, 3.4.7, 3.6.1, 3.6.2, 3.6.3, 3.6.4, 3.7.1, 
4.1.1, 4.3.3, 4.3.5, 5.1.2, 5.1.3a, 5.1.3b, 5.2.1, 5.2.2, 5.2.3, 
5.3.1, 5.3.2, 5.3.3, 5.4.1, 6.3.3, 6.3.4, 6.5.2, 
7.1.1, 7.1.8, 7.1.9, 7.1.10, 7.1.11, 7.2.1, 7.3.1
```
- Columns: metric_number, metric_description, criteria_number

#### Sheet 4: "Submission_Log" (Auto-create for tracking)
- Columns: timestamp, institute_code, institute_name, metric_number, validation_status, error_count, file_name, submitted_to_qmb, user_info
- Logs every validation attempt and submission

### 2. Frontend Web Application

#### Homepage Design (Symbiosis Branded)
```html
<!-- Clean, professional interface with red (#D32F2F) and blue (#1976D2) branding -->
<div class="container">
  <header class="header-section">
    <h1>NAAC AQAR CSV Validation Tool</h1>
    <p>Symbiosis International University - Quality Assurance</p>
  </header>
  
  <div class="main-form">
    <!-- Institution Dropdown -->
    <div class="form-group">
      <label>Select Your Institution:</label>
      <select id="institutionSelect" class="form-control">
        <option value="">Choose Institution...</option>
        <!-- Populate with all 43 institutions -->
        <option value="101">101 - SLS-P - Symbiosis Law School, Pune</option>
        <option value="102">102 - SLS-N - Symbiosis Law School, Noida</option>
        <!-- ... all 43 institutions -->
      </select>
    </div>
    
    <!-- Metric Dropdown -->
    <div class="form-group">
      <label>Select Metric:</label>
      <select id="metricSelect" class="form-control">
        <option value="">Choose Metric...</option>
        <!-- Populate with all 53 metrics -->
        <option value="1.1.1">1.1.1 - Curricula developed and implemented...</option>
        <option value="1.1.1a">1.1.1a - Global developmental needs...</option>
        <!-- ... all 52 metrics -->
      </select>
    </div>
    
    <!-- CSV Upload -->
    <div class="form-group">
      <label>Upload CSV File:</label>
      <input type="file" id="csvFile" accept=".csv" class="form-control">
    </div>
    
    <!-- Action Buttons -->
    <div class="button-group">
      <button id="validateBtn" class="btn btn-primary">Validate CSV</button>
      <button id="submitBtn" class="btn btn-success" disabled>Submit to QMB</button>
      <button id="downloadTemplateBtn" class="btn btn-info">Download Template</button>
    </div>
    
    <!-- Results Section -->
    <div id="resultsSection" style="display: none;">
      <div id="validationResults"></div>
      <button id="downloadErrorReport" class="btn btn-warning" style="display: none;">Download Error Report</button>
    </div>
  </div>
</div>
```

### 3. Core Functionality

#### ValidationEngine.gs
```javascript
function validateCSV(csvData, metricNumber, instituteCode) {
  /* CRITICAL: Implement ALL 928 validation rules
     - Get validation rules for specific metric from Validation_Rules sheet
     - Process each row and column against ALL validation types:
       year_range, exact_match, contains_text, faculty_choice, institute_code_choice,
       institute_shortname_choice, institute_name_choice, fixed_digits, required_field,
       word_limit, pdf_file, yes_no_choice, binary_choice, text_length, year,
       numeric, currency, url_pattern, image_file, date, reference_lookup
     - Return detailed error report with row/column references
  */
  
  const validationRules = getValidationRules(metricNumber);
  const errors = [];
  const csvRows = parseCSV(csvData);
  
  // Validate each row against all rules
  for (let rowIndex = 0; rowIndex < csvRows.length; rowIndex++) {
    for (let rule of validationRules) {
      const value = csvRows[rowIndex][rule.column_name];
      const validationResult = validateField(value, rule);
      
      if (!validationResult.isValid) {
        errors.push({
          row: rowIndex + 2, // +2 for header and 1-based indexing
          column: rule.column_name,
          value: value,
          error: rule.error_message,
          rule: rule.validation_type
        });
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errorCount: errors.length,
    errors: errors,
    summary: generateValidationSummary(errors)
  };
}

function validateField(value, rule) {
  // Implement ALL validation types exactly as specified
  switch (rule.validation_type) {
    case 'year_range':
      return validateYearRange(value, rule.validation_params);
    case 'exact_match':
      return validateExactMatch(value, rule.validation_params);
    case 'required_field':
      return validateRequiredField(value, rule.validation_params);
    // ... implement all validation types
  }
}
```

#### FileManager.gs
```javascript
function submitToQMB(csvData, instituteCode, instituteName, metricNumber) {
  /* SIMPLE SUBMISSION PROCESS:
     - Create folder: /NAAC_AQAR_Submissions/{Institute_Code}_{Institute_Short_Name}/
     - Save file as: {Institute_Code}_{Metric_Number}_{Timestamp}.csv
     - Log submission in Submission_Log sheet
     - Return confirmation with file location
  */
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '');
  const fileName = `${instituteCode}_${metricNumber}_${timestamp}.csv`;
  const folderName = `${instituteCode}_${getInstituteShortName(instituteCode)}`;
  
  // Create folder structure and save file
  const folder = createOrGetFolder(folderName);
  const file = folder.createFile(fileName, csvData, 'text/csv');
  
  // Log submission
  logSubmission(instituteCode, instituteName, metricNumber, fileName, true);
  
  return {
    success: true,
    fileName: fileName,
    fileUrl: file.getUrl(),
    message: `File successfully submitted to QMB`
  };
}
```

#### TemplateGenerator.gs
```javascript
function downloadTemplate(metricNumber) {
  /* TEMPLATE GENERATION:
     - Extract column structure from validation rules for specific metric
     - Generate CSV template with proper headers and sample data
     - Include validation hints in comments
  */
  
  const rules = getValidationRules(metricNumber);
  const columns = [...new Set(rules.map(rule => rule.column_name))];
  
  // Generate template with headers and sample data
  let template = columns.join(',') + '\n';
  
  // Add sample row with realistic data based on validation_params
  const sampleRow = columns.map(col => generateSampleValue(col, rules)).join(',');
  template += sampleRow;
  
  return template;
}
```

### 4. User Interface Features

#### Real-time Validation Display
```javascript
function displayValidationResults(results) {
  const resultsDiv = document.getElementById('validationResults');
  
  if (results.isValid) {
    resultsDiv.innerHTML = `
      <div class="alert alert-success">
        <h4>✅ Validation Successful!</h4>
        <p>Your CSV file passed all validation checks. You can now submit it to QMB.</p>
      </div>
    `;
    document.getElementById('submitBtn').disabled = false;
  } else {
    resultsDiv.innerHTML = `
      <div class="alert alert-danger">
        <h4>❌ Validation Failed</h4>
        <p>Found ${results.errorCount} errors in your CSV file:</p>
        <div class="error-list">
          ${results.errors.map(error => `
            <div class="error-item">
              <strong>Row ${error.row}, Column "${error.column}":</strong> ${error.error}
              <br><small>Value: "${error.value}" | Rule: ${error.rule}</small>
            </div>
          `).join('')}
        </div>
      </div>
    `;
    document.getElementById('downloadErrorReport').style.display = 'block';
    document.getElementById('submitBtn').disabled = true;
  }
  
  document.getElementById('resultsSection').style.display = 'block';
}
```

#### Error Report Generation
```javascript
function generateErrorReport(errors, instituteCode, metricNumber) {
  /* DOWNLOADABLE ERROR REPORT:
     - Excel format with detailed error information
     - Include suggestions for fixing each error
     - Institution and metric information
     - Timestamp and summary statistics
  */
  
  const reportData = [
    ['NAAC AQAR Validation Error Report'],
    [`Institution: ${instituteCode}`, `Metric: ${metricNumber}`],
    [`Generated: ${new Date().toLocaleString()}`],
    [''],
    ['Row', 'Column', 'Error Message', 'Current Value', 'Validation Rule'],
    ...errors.map(error => [error.row, error.column, error.error, error.value, error.rule])
  ];
  
  // Convert to Excel format and trigger download
  return createExcelFile(reportData, `Validation_Errors_${instituteCode}_${metricNumber}.xlsx`);
}
```

### 5. Complete Workflow

1. **User selects institution** from dropdown (all 43 institutions)
2. **User selects metric** from dropdown (all 53 metrics)
3. **User uploads CSV file**
4. **User clicks "Validate CSV"**:
   - System validates against ALL 928 rules
   - Shows detailed error report if errors found
   - Enables "Submit to QMB" button if validation passes
5. **If validation fails**:
   - User can download detailed error report
   - User fixes errors and re-validates
6. **If validation passes**:
   - User clicks "Submit to QMB"
   - File is saved to organized Google Drive folder
   - Submission is logged in tracking sheet
   - User receives confirmation

### 6. Google Drive Organization

```
📁 NAAC_AQAR_Submissions/
  📁 101_SLS-P/
    📄 101_SLS-P_1.1.1_20240315143022.csv
    📄 101_SLS-P_2.3.1_20240315150245.csv
  📁 102_SLS-N/
    📄 102_SLS-N_1.1.1_20240315144530.csv
  📁 201_SIBM-P/
    📄 201_SIBM-P_1.1.1_20240315145500.csv
  [... folders for all institutions as needed]
```

## Implementation Requirements

### Automated Setup Function
```javascript
function initializeSimpleSystem() {
  // 1. Create spreadsheet with 4 sheets
  // 2. Import validation rules CSV (ALL 905 rules)
  // 3. Populate institution list (ALL 43 institutions)
  // 4. Extract metrics list (ALL 52 metrics)
  // 5. Setup submission logging
  // 6. Create Google Drive folder structure
  // 7. Deploy web app
}
```

### Quality Standards
- ✅ **Simple Interface**: Easy dropdown selections and clear buttons
- ✅ **Complete Validation**: ALL 905 rules implemented without exception
- ✅ **Clear Error Reporting**: Detailed, user-friendly error messages
- ✅ **Efficient Workflow**: Validate first, submit only if error-free
- ✅ **Professional Design**: Symbiosis branding with red/blue colors
- ✅ **Organized Storage**: Structured file naming and folder organization
- ✅ **Complete Logging**: Track all validation attempts and submissions
- ✅ **No Authentication**: Open access for simplicity
- ✅ **Responsive Design**: Works on desktop and mobile devices

### Success Criteria
- Any SIU staff member can access and use the tool immediately
- All 43 institutions appear in dropdown
- All 53 metrics available for selection
- Every validation rule works correctly
- Error reports are clear and actionable
- Successful submissions are properly organized
- Tool is fast, reliable, and user-friendly

**DELIVERY**: Complete, working Google Apps Script web application with all sheets pre-configured and ready for immediate use by all SIU institutions.
